<?php
/**
 * Test Frontend Fixes
 * Test the JavaScript and AJAX fixes
 */

// Include WordPress
require_once('../../../wp-load.php');

// Check if user is logged in
if (!is_user_logged_in()) {
    echo "Please log in to test frontend fixes.";
    exit;
}

$user_id = get_current_user_id();
$nonce = wp_create_nonce('lexai_fullpage_nonce');

?>
<!DOCTYPE html>
<html>
<head>
    <title>LexAI Frontend Fixes Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px; }
        #results { margin-top: 20px; padding: 10px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>🧪 LexAI Frontend Fixes Test</h1>
    
    <div class="test-section">
        <h2>1. jQuery Availability Test</h2>
        <button onclick="testJQuery()">Test jQuery</button>
        <div id="jquery-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. AJAX Endpoints Test</h2>
        <button onclick="testCreateConversation()">Test Create Conversation</button>
        <button onclick="testSendMessage()">Test Send Message</button>
        <div id="ajax-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Configuration Test</h2>
        <button onclick="testConfig()">Test Config</button>
        <div id="config-result"></div>
    </div>
    
    <div id="results"></div>

    <script>
        // Configuration object (simulating wp_localize_script)
        const lexaiConfig = {
            ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
            nonce: '<?php echo $nonce; ?>',
            userId: <?php echo $user_id; ?>,
            userRole: 'administrator'
        };

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = new Date().toLocaleTimeString() + ': ' + message;
            results.appendChild(div);
        }

        function testJQuery() {
            const result = document.getElementById('jquery-result');
            
            if (typeof jQuery !== 'undefined') {
                result.innerHTML = '<span class="success">✅ jQuery is available (version: ' + jQuery.fn.jquery + ')</span>';
                log('jQuery test passed', 'success');
                
                // Test jQuery AJAX
                jQuery.ajax({
                    url: lexaiConfig.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'heartbeat',
                        _wpnonce: lexaiConfig.nonce
                    },
                    success: function(response) {
                        log('jQuery AJAX test passed', 'success');
                    },
                    error: function() {
                        log('jQuery AJAX test failed', 'error');
                    }
                });
            } else {
                result.innerHTML = '<span class="error">❌ jQuery is not available</span>';
                log('jQuery test failed', 'error');
            }
        }

        function testCreateConversation() {
            const result = document.getElementById('ajax-result');
            result.innerHTML = 'Testing create conversation...';
            
            jQuery.ajax({
                url: lexaiConfig.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'lexai_create_conversation',
                    nonce: lexaiConfig.nonce,
                    title: 'Test Conversation'
                },
                success: function(response) {
                    if (response.success) {
                        result.innerHTML = '<span class="success">✅ Create conversation works! ID: ' + response.data.conversation_id + '</span>';
                        log('Create conversation test passed', 'success');
                    } else {
                        result.innerHTML = '<span class="error">❌ Create conversation failed: ' + response.data.message + '</span>';
                        log('Create conversation test failed: ' + response.data.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    result.innerHTML = '<span class="error">❌ AJAX error: ' + error + '</span>';
                    log('Create conversation AJAX error: ' + error, 'error');
                }
            });
        }

        function testSendMessage() {
            const result = document.getElementById('ajax-result');
            result.innerHTML = 'Testing send message...';

            log('Starting send message test...', 'info');
            log('AJAX URL: ' + lexaiConfig.ajaxUrl, 'info');
            log('Nonce: ' + lexaiConfig.nonce, 'info');

            jQuery.ajax({
                url: lexaiConfig.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'lexai_send_message_fullpage',
                    nonce: lexaiConfig.nonce,
                    message: 'Test message from frontend',
                    conversation_id: 1
                },
                timeout: 30000, // 30 second timeout
                beforeSend: function() {
                    log('Sending AJAX request...', 'info');
                },
                success: function(response) {
                    log('AJAX success callback triggered', 'success');
                    log('Response: ' + JSON.stringify(response), 'info');

                    if (response.success) {
                        result.innerHTML = '<span class="success">✅ Send message works! Response: ' + response.data.message.substring(0, 100) + '...</span>';
                        log('Send message test passed', 'success');
                    } else {
                        result.innerHTML = '<span class="error">❌ Send message failed: ' + (response.data ? response.data.message : 'Unknown error') + '</span>';
                        log('Send message test failed: ' + (response.data ? response.data.message : 'Unknown error'), 'error');
                    }
                },
                error: function(xhr, status, error) {
                    log('AJAX error callback triggered', 'error');
                    log('XHR status: ' + xhr.status, 'error');
                    log('Status: ' + status, 'error');
                    log('Error: ' + error, 'error');
                    log('Response text: ' + xhr.responseText, 'error');

                    result.innerHTML = '<span class="error">❌ AJAX error: ' + error + ' (Status: ' + status + ')</span>';
                    log('Send message AJAX error: ' + error, 'error');
                },
                complete: function() {
                    log('AJAX request completed', 'info');
                }
            });
        }

        function testConfig() {
            const result = document.getElementById('config-result');
            
            if (lexaiConfig && lexaiConfig.ajaxUrl && lexaiConfig.nonce && lexaiConfig.userId) {
                result.innerHTML = '<span class="success">✅ Configuration is valid</span>';
                log('Configuration test passed', 'success');
                log('AJAX URL: ' + lexaiConfig.ajaxUrl, 'info');
                log('User ID: ' + lexaiConfig.userId, 'info');
                log('Nonce: ' + lexaiConfig.nonce.substring(0, 10) + '...', 'info');
            } else {
                result.innerHTML = '<span class="error">❌ Configuration is invalid</span>';
                log('Configuration test failed', 'error');
            }
        }

        // Auto-run tests when page loads
        jQuery(document).ready(function() {
            log('Page loaded, running automatic tests...', 'info');
            testJQuery();
            testConfig();
        });
    </script>
</body>
</html>
