// Wait for jQuery to be available
function testAuth() {
    if (typeof jQuery === 'undefined') {
        console.log('❌ jQuery not available yet, retrying...');
        setTimeout(testAuth, 100);
        return;
    }

    jQuery(function($) {
        console.log('=== LexAI Authentication Debug ===');

        // Check if lexaiConfig exists
        if (typeof lexaiConfig !== 'undefined' && lexaiConfig.ajaxUrl) {
            console.log('✅ lexaiConfig found:', lexaiConfig);
            console.log('User ID:', lexaiConfig.userId);
            console.log('Nonce:', lexaiConfig.nonce);
            console.log('AJAX URL:', lexaiConfig.ajaxUrl);

            // Test AJAX endpoint
            console.log('Testing authentication...');
            $.ajax({
                url: lexaiConfig.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'lexai_send_message_fullpage',
                    nonce: lexaiConfig.nonce,
                    message: 'test'
                },
                success: function(response) {
                    console.log('✅ AJAX Test Success:', response);
                },
                error: function(xhr, status, error) {
                    console.log('❌ AJAX Test Error:', {
                        status: status,
                        error: error,
                        responseText: xhr.responseText,
                        statusCode: xhr.status
                    });
                }
            });

        } else {
            console.log('❌ lexaiConfig not found or misconfigured. Cannot test authentication.');
        }

        // Also check WordPress user info object
        if (typeof wp !== 'undefined' && wp.ajax) {
            console.log('✅ WordPress (wp.ajax) object available.');
        } else {
            console.log('❌ WordPress (wp.ajax) object not available.');
        }

        console.log('=== End Debug ===');
    });
}

// Start the test when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', testAuth);
} else {
    testAuth();
}