jQuery(function($) {
    // This script runs when the DOM is ready, and jQuery is passed as $

    console.log('=== LexAI Authentication Debug ===');

    // Check if lexaiConfig exists
    if (typeof lexaiConfig !== 'undefined' && lexaiConfig.ajaxUrl) {
        console.log('✅ lexaiConfig found:', lexaiConfig);
        console.log('User ID:', lexaiConfig.userId);
        console.log('Nonce:', lexaiConfig.nonce);
        console.log('AJAX URL:', lexaiConfig.ajaxUrl);

        // Test AJAX endpoint
        console.log('Testing authentication via AJAX...');
        $.ajax({
            url: lexaiConfig.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lexai_test_auth', // A simple action for testing auth
                nonce: lexaiConfig.nonce
            },
            success: function(response) {
                console.log('✅ AJAX Auth Test Success:', response);
            },
            error: function(xhr, status, error) {
                console.log('❌ AJAX Auth Test Error:', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText,
                    statusCode: xhr.status
                });
            }
        });

    } else {
        console.log('❌ lexaiConfig not found or misconfigured. Cannot test authentication.');
    }

    // Also check WordPress user info object
    if (typeof wp !== 'undefined' && wp.ajax) {
        console.log('✅ WordPress (wp.ajax) object available.');
    } else {
        console.log('❌ WordPress (wp.ajax) object not available.');
    }

    console.log('=== End Debug ===');
});