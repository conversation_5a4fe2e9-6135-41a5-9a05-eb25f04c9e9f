/**
 * LexAI Full Page Chat Interface JavaScript
 * Modern, responsive chat interface with advanced features
 * 
 * @package LexAI
 * @since 1.2.3
 */

(function($) {
    'use strict';

    // Prevent multiple initializations
    if (window.lexaiFullPageInitialized) {
        console.log('LexAI Full Page Chat already initialized, skipping...');
        return;
    }
    window.lexaiFullPageInitialized = true;

    // Prevent conflicts with other plugins (like Elementor) that might declare lazyloadRunObserver
    if (typeof window.lazyloadRunObserver !== 'undefined') {
        console.warn('LexAI: lazyloadRunObserver already exists, skipping redefinition to prevent conflicts');
    }

    // Global variables
    let lexaiConfig = {};
    let currentConversationId = null;
    let isProcessing = false;
    let mediaRecorder = null;
    let audioChunks = [];
    let isRecording = false;
    let currentTheme = 'corporate';
    let sidebarOpen = false;
    let filePreviewsVisible = false;

    /**
     * Initialize the chat interface
     */
    function initLexAI() {
        loadConfiguration();
        initEventListeners();
        initTheme();
        loadConversations();
        setupFileHandling();
        setupVoiceRecording();
        setupKeyboardShortcuts();
        
        // Hide welcome screen if there are existing messages
        checkForExistingMessages();
        
        console.log('LexAI Full Page Chat initialized');
    }

    /**
     * Load configuration from wp_localize_script
     */
    function loadConfiguration() {
        // First try to get config from wp_localize_script
        if (typeof window.lexaiConfig !== 'undefined') {
            lexaiConfig = window.lexaiConfig;
            console.log('LexAI: Configuration loaded from wp_localize_script');
            return;
        }

        // Fallback to script tag method
        const configElement = document.getElementById('lexai-fullpage-config');
        if (configElement) {
            try {
                lexaiConfig = JSON.parse(configElement.textContent);
                console.log('LexAI: Configuration loaded from script tag');
            } catch (e) {
                console.error('LexAI: Error parsing configuration', e);
                lexaiConfig = {};
            }
        } else {
            console.error('LexAI: No configuration found');
            lexaiConfig = {};
        }
    }

    /**
     * Initialize all event listeners
     */
    function initEventListeners() {
        // Sidebar toggle
        $('#lexai-sidebar-toggle').on('click', toggleSidebar);
        
        // New chat button
        $('#lexai-new-chat').on('click', startNewChat);
        
        // Message input
        $('#lexai-message-input').on('input', handleInputChange);
        $('#lexai-message-input').on('keydown', handleInputKeydown);
        
        // Send button
        $('#lexai-send-btn').on('click', sendMessage);
        
        // File attachment
        $('#lexai-attach-btn').on('click', () => $('#lexai-file-input').click());
        $('#lexai-file-input').on('change', handleFileSelection);
        
        // Voice recording
        $('#lexai-voice-btn').on('click', toggleVoiceRecording);
        
        // Theme toggle
        $('#lexai-theme-toggle').on('click', toggleTheme);
        
        // Quick actions
        $(document).on('click', '.lexai-quick-action', handleQuickAction);
        
        // User menu
        $('#lexai-user-menu').on('click', toggleUserMenu);
        $(document).on('click', function(e) {
            if (!$(e.target).closest('#lexai-user-menu, #lexai-user-menu-dropdown').length) {
                hideUserMenu();
            }
        });
        
        // Modal controls
        $('.lexai-modal-close').on('click', closeModal);
        $(document).on('click', '.lexai-modal', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
        
        // Export functionality
        $('#lexai-export-chat').on('click', showExportModal);
        $(document).on('click', '.lexai-export-btn', handleExport);
        
        // Clear chat
        $('#lexai-clear-chat').on('click', clearChat);
        
        // Settings
        $('#lexai-settings').on('click', showSettingsModal);
        
        // Profile and subscription modals
        $('#lexai-profile-settings').on('click', showProfileModal);
        $('#lexai-subscription-plans').on('click', showSubscriptionModal);
        
        // Sidebar overlay for mobile
        $('.lexai-sidebar-overlay').on('click', closeSidebar);
        
        // Auto-resize textarea
        autoResizeTextarea();
        
        // Drag and drop for files
        setupDragAndDrop();

        // Delegated event handlers for message actions
        $('#lexai-messages-container').on('click', '.lexai-copy-btn', handleCopyMessage);
        $('#lexai-messages-container').on('click', '.lexai-tts-btn', handleTTS);
        $('#lexai-messages-container').on('click', '.lexai-toggle-btn', handleToggleMessage);
        $('#lexai-messages-container').on('click', '.lexai-export-option', handleExportMessage);
    }

    /**
     * Initialize theme system
     */
    function initTheme() {
        // Load saved theme
        const savedTheme = localStorage.getItem('lexai-theme') || 'corporate';
        setTheme(savedTheme);
    }

    /**
     * Set theme
     */
    function setTheme(theme) {
        currentTheme = theme;
        
        // Remove existing theme classes
        $('body').removeClass('lexai-theme-dark lexai-theme-corporate');
        
        // Add new theme class
        if (theme === 'dark') {
            $('body').addClass('lexai-theme-dark');
        } else {
            $('body').addClass('lexai-theme-corporate');
        }
        
        // Save theme preference
        localStorage.setItem('lexai-theme', theme);
        
        // Update theme toggle icon
        updateThemeToggleIcon();
    }

    /**
     * Toggle theme
     */
    function toggleTheme() {
        const newTheme = currentTheme === 'corporate' ? 'dark' : 'corporate';
        setTheme(newTheme);
        
        showToast('success', 'Tema cambiado', `Tema ${newTheme === 'dark' ? 'oscuro' : 'corporativo'} activado`);
    }

    /**
     * Update theme toggle icon
     */
    function updateThemeToggleIcon() {
        const lightIcon = $('.lexai-theme-icon-light');
        const darkIcon = $('.lexai-theme-icon-dark');
        
        if (currentTheme === 'dark') {
            lightIcon.css({ opacity: 0, transform: 'rotate(180deg)' });
            darkIcon.css({ opacity: 1, transform: 'rotate(0deg)' });
        } else {
            lightIcon.css({ opacity: 1, transform: 'rotate(0deg)' });
            darkIcon.css({ opacity: 0, transform: 'rotate(-180deg)' });
        }
    }

    /**
     * Toggle sidebar
     */
    function toggleSidebar() {
        if (window.innerWidth <= 768) {
            // Mobile behavior
            sidebarOpen = !sidebarOpen;
            if (sidebarOpen) {
                openSidebar();
            } else {
                closeSidebar();
            }
        } else {
            // Desktop behavior - collapse/expand
            $('.lexai-sidebar').toggleClass('collapsed');
        }
    }

    /**
     * Open sidebar (mobile)
     */
    function openSidebar() {
        $('.lexai-sidebar').addClass('open');
        $('.lexai-sidebar-overlay').addClass('show');
        $('body').addClass('sidebar-open');
        sidebarOpen = true;
    }

    /**
     * Close sidebar (mobile)
     */
    function closeSidebar() {
        $('.lexai-sidebar').removeClass('open');
        $('.lexai-sidebar-overlay').removeClass('show');
        $('body').removeClass('sidebar-open');
        sidebarOpen = false;
    }

    /**
     * Handle input changes
     */
    function handleInputChange() {
        const input = $('#lexai-message-input');
        const value = input.val();
        const length = value.length;
        
        // Update character counter
        $('#lexai-char-count').text(length);
        
        // Enable/disable send button
        const hasContent = length > 0 && length <= 2000;
        $('#lexai-send-btn').prop('disabled', !hasContent || isProcessing);
        
        // Auto-resize textarea
        autoResizeTextarea();
    }

    /**
     * Handle input keydown events
     */
    function handleInputKeydown(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    }

    /**
     * Auto-resize textarea
     */
    function autoResizeTextarea() {
        const textarea = $('#lexai-message-input')[0];
        if (textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }
    }

    /**
     * Send message
     */
    function sendMessage() {
        if (isProcessing) return;

        const message = $('#lexai-message-input').val().trim();
        if (!message) return;

        hideWelcomeScreen();
        addMessage('user', message);
        $('#lexai-message-input').val('');
        handleInputChange();
        showTypingIndicator();
        setProcessingState(true);

        // Create a new conversation if one doesn't exist
        if (!currentConversationId) {
            $.ajax({
                url: lexaiConfig.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'lexai_create_conversation',
                    nonce: lexaiConfig.nonce,
                    title: message.substring(0, 50) + '...'
                },
                success: function(response) {
                    if (response.success) {
                        currentConversationId = response.data.conversation_id;
                        updateConversationsList();
                        startProcessing(message);
                    } else {
                        showErrorMessage('No se pudo crear una nueva conversación.');
                        setProcessingState(false);
                        hideTypingIndicator();
                    }
                },
                error: function() {
                    showErrorMessage('Error de red al crear la conversación.');
                    setProcessingState(false);
                    hideTypingIndicator();
                }
            });
        } else {
            startProcessing(message);
        }
    }

    function startProcessing(message) {
        $.ajax({
            url: lexaiConfig.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lexai_start_chat_processing',
                nonce: lexaiConfig.nonce,
                message: message,
                conversation_id: currentConversationId
            },
            success: function(response) {
                if (response.success) {
                    startPolling(response.data.task_id);
                } else {
                    showErrorMessage(response.data.message || 'Error al iniciar el procesamiento.');
                    setProcessingState(false);
                    hideTypingIndicator();
                }
            },
            error: function() {
                showErrorMessage('Error de red al iniciar el procesamiento.');
                setProcessingState(false);
                hideTypingIndicator();
            }
        });
    }

    let pollingInterval = null;

    function startPolling(taskId) {
        let lastMessageId = 0;
        // Get the last message ID from the current chat to avoid fetching old messages
        const lastMessage = $('#lexai-messages-container .lexai-message[data-message-id]').last();
        if (lastMessage.length) {
            lastMessageId = parseInt(lastMessage.data('message-id')) || 0;
        }

        // Clear any existing polling interval
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }

        pollingInterval = setInterval(() => {
            pollStatus(taskId, lastMessageId, (newLastId) => {
                lastMessageId = newLastId;
            });
        }, 3000); // Poll every 3 seconds
    }

    function pollStatus(taskId, lastMessageId, updateLastIdCallback) {
        $.ajax({
            url: lexaiConfig.ajaxUrl,
            type: 'GET',
            data: {
                action: 'lexai_check_chat_status',
                nonce: lexaiConfig.nonce,
                task_id: taskId,
                last_message_id: lastMessageId
            },
            success: function(response) {
                if (response.success) {
                    let newLastId = lastMessageId;
                    if (response.data.messages && response.data.messages.length > 0) {
                        hideTypingIndicator(); // Hide indicator once first partial message arrives
                        response.data.messages.forEach(msg => {
                            displayPartialMessage(msg);
                            if (msg.id > newLastId) {
                                newLastId = msg.id;
                            }
                        });
                        updateLastIdCallback(newLastId);
                    }

                    if (response.data.status === 'completed' || response.data.status === 'failed') {
                        clearInterval(pollingInterval);
                        setProcessingState(false);
                        hideTypingIndicator();
                        updateConversationsList(); // Update sidebar with latest status
                    }
                } else {
                    showErrorMessage(response.data.message || 'Error durante el sondeo.');
                    clearInterval(pollingInterval);
                    setProcessingState(false);
                    hideTypingIndicator();
                }
            },
            error: function() {
                showErrorMessage('Error de red durante el sondeo.');
                clearInterval(pollingInterval);
                setProcessingState(false);
                hideTypingIndicator();
            }
        });
    }

    function displayPartialMessage(message) {
        if ($('.lexai-message[data-message-id="' + message.id + '"]').length) {
            return; // Avoid duplicates
        }

        const metadata = JSON.parse(message.metadata || '{}');
        const type = metadata.type || 'message';
        const audioUrl = metadata.audio_url || null;

        const time = formatTime(message.created_at);
        const content = formatMessage(message.content);

        const messageHtml = `
            <div class="lexai-message lexai-message-assistant lexai-partial-${type}" data-message-id="${message.id}" ${audioUrl ? `data-audio-url="${audioUrl}"` : ''}>
                <div class="lexai-message-avatar">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <div class="lexai-message-content">
                    <div class="lexai-message-text">${content}</div>
                    <div class="lexai-message-time">${time}</div>
                </div>
            </div>
        `;
        
        $('#lexai-messages-container').append(messageHtml);
        scrollToBottom();
    }

    /**
     * Add message to chat
     */
    function addMessage(role, content, timestamp = null, audioUrl = null) {
        const time = timestamp || new Date().toLocaleTimeString();
        const isUser = role === 'user';
        
        const messageHtml = `
            <div class="lexai-message ${isUser ? 'lexai-message-user' : 'lexai-message-assistant'}" ${audioUrl ? `data-audio-url="${audioUrl}"` : ''}>
                <div class="lexai-message-avatar">
                    ${isUser ? '<i class="fas fa-user"></i>' : '<i class="fas fa-balance-scale"></i>'}
                </div>
                <div class="lexai-message-content">
                    <div class="lexai-message-text">${isUser ? escapeHtml(content) : formatMessage(content)}</div>
                    <div class="lexai-message-time">${time}</div>
                    ${!isUser ? `
                        <div class="lexai-message-actions">
                            <button class="lexai-message-action-btn lexai-copy-btn"><i class="fas fa-copy"></i></button>
                            <button class="lexai-message-action-btn lexai-tts-btn"><i class="fas fa-volume-up"></i></button>
                            <button class="lexai-message-action-btn lexai-toggle-btn"><i class="fas fa-chevron-up"></i></button>
                            <div class="lexai-export-menu">
                                <button class="lexai-message-action-btn lexai-export-btn"><i class="fas fa-file-export"></i></button>
                                <div class="lexai-export-options">
                                    <a href="#" class="lexai-export-option" data-format="pdf">PDF</a>
                                    <a href="#" class="lexai-export-option" data-format="docx">DOCX</a>
                                    <a href="#" class="lexai-export-option" data-format="md">Markdown</a>
                                </div>
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
        
        $('#lexai-messages-container').append(messageHtml);
        scrollToBottom();
    }

    /**
     * Handle copy message button click
     */
    function handleCopyMessage() {
        const messageText = $(this).closest('.lexai-message-content').find('.lexai-message-text').text();
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(messageText).then(function() {
                showToast('success', 'Copiado', 'Mensaje copiado al portapapeles');
            });
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = messageText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showToast('success', 'Copiado', 'Mensaje copiado al portapapeles');
        }
    }

    /**
     * Handle Text-to-Speech button click
     */
    function handleTTS() {
        const messageElement = $(this).closest('.lexai-message');
        const messageText = messageElement.find('.lexai-message-text').text();
        const messageId = messageElement.data('message-id');
        const existingAudioUrl = messageElement.data('audio-url');

        if (!messageText) {
            showToast('error', 'Error', 'No hay texto para reproducir.');
            return;
        }

        if (existingAudioUrl) {
            // Play existing audio
            const audio = new Audio(existingAudioUrl);
            audio.play();
            showToast('success', 'Reproduciendo', 'Audio existente reproducido.');
            return;
        }

        // Show loading indicator on the button
        const ttsButton = $(this);
        ttsButton.find('i').removeClass('fa-volume-up').addClass('fa-spinner fa-spin');
        ttsButton.prop('disabled', true);

        $.ajax({
            url: lexaiConfig.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lexai_generate_tts',
                nonce: lexaiConfig.nonce,
                text: messageText,
                message_id: messageId // Pass message ID for logging/tracking
            },
            success: function(response) {
                if (response.success && response.data.audio_url) {
                    const audio = new Audio(response.data.audio_url);
                    audio.play();
                    showToast('success', 'Reproduciendo', 'Audio generado y reproduciéndose.');
                } else {
                    showToast('error', 'Error TTS', response.data.message || 'No se pudo generar el audio.');
                }
            },
            error: function() {
                showToast('error', 'Error de red', 'Error al conectar con el servicio de audio.');
            },
            complete: function() {
                ttsButton.find('i').removeClass('fa-spinner fa-spin').addClass('fa-volume-up');
                ttsButton.prop('disabled', false);
            }
        });
    }

    /**
     * Handle toggle message (expand/collapse)
     */
    function handleToggleMessage() {
        const messageContent = $(this).closest('.lexai-message-content').find('.lexai-message-text');
        const icon = $(this).find('i');

        messageContent.toggleClass('expanded');
        if (messageContent.hasClass('expanded')) {
            icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
        } else {
            icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
        }
        scrollToBottom(); // Adjust scroll after expanding/collapsing
    }

    /**
     * Handle export individual message
     */
    function handleExportMessage() {
        const format = $(this).data('format');
        const messageElement = $(this).closest('.lexai-message');
        const messageText = messageElement.find('.lexai-message-text').text();
        const messageId = messageElement.data('message-id');

        if (!messageText) {
            showToast('error', 'Error', 'No hay contenido para exportar.');
            return;
        }

        // Show loading state on the export button
        const exportButton = $(this).closest('.lexai-export-menu').find('.lexai-export-btn');
        exportButton.find('i').removeClass('fa-file-export').addClass('fa-spinner fa-spin');
        exportButton.prop('disabled', true);

        $.ajax({
            url: lexaiConfig.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lexai_export_single_message',
                nonce: lexaiConfig.nonce,
                message_id: messageId,
                content: messageText,
                format: format
            },
            success: function(response) {
                if (response.success && response.data.download_url) {
                    const link = document.createElement('a');
                    link.href = response.data.download_url;
                    link.download = response.data.filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    showToast('success', 'Exportado', `Mensaje exportado a ${format.toUpperCase()}.`);
                } else {
                    showToast('error', 'Error de exportación', response.data.message || 'No se pudo exportar el mensaje.');
                }
            },
            error: function() {
                showToast('error', 'Error de red', 'Error al conectar con el servicio de exportación.');
            },
            complete: function() {
                exportButton.find('i').removeClass('fa-spinner fa-spin').addClass('fa-file-export');
                exportButton.prop('disabled', false);
            }
        });
    }

    /**
     * Show error message
     */
    function showErrorMessage(message) {
        const errorHtml = `
            <div class="lexai-message lexai-message-error">
                <div class="lexai-message-avatar">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="lexai-message-content">
                    <div class="lexai-message-text">
                        <strong>Error:</strong> ${escapeHtml(message)}
                    </div>
                    <div class="lexai-message-actions">
                        <button class="lexai-message-action-btn" onclick="retryLastMessage()">
                            <i class="fas fa-redo"></i> Reintentar
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        $('#lexai-messages-container').append(errorHtml);
        scrollToBottom();
    }

    /**
     * Show typing indicator
     */
    function showTypingIndicator() {
        $('#lexai-typing-indicator').show();
        scrollToBottom();
    }

    /**
     * Hide typing indicator
     */
    function hideTypingIndicator() {
        $('#lexai-typing-indicator').hide();
    }

    /**
     * Set processing state
     */
    function setProcessingState(processing) {
        isProcessing = processing;
        $('#lexai-send-btn').prop('disabled', processing || !$('#lexai-message-input').val().trim());
        
        if (processing) {
            $('#lexai-send-btn').addClass('lexai-loading');
        } else {
            $('#lexai-send-btn').removeClass('lexai-loading');
        }
    }

    /**
     * Hide welcome screen
     */
    function hideWelcomeScreen() {
        $('#lexai-welcome-screen').fadeOut(300);
    }

    /**
     * Show welcome screen
     */
    function showWelcomeScreen() {
        $('#lexai-welcome-screen').fadeIn(300);
    }

    /**
     * Handle quick actions
     */
    function handleQuickAction() {
        const query = $(this).data('query');
        if (query) {
            $('#lexai-message-input').val(query);
            sendMessage();
        }
    }

    /**
     * Start new chat
     */
    function startNewChat() {
        currentConversationId = null;
        // Clear only message elements, keep welcome screen
        $('#lexai-messages-container').children('.lexai-message').remove();
        $('#lexai-current-title').text('Nueva conversación');
        showWelcomeScreen();

        // Clear any file previews
        clearFilePreviews();

        showToast('info', 'Nueva conversación', 'Conversación iniciada');
    }

    /**
     * Clear chat
     */
    function clearChat() {
        if (confirm('¿Estás seguro de que quieres limpiar esta conversación?')) {
            // Clear only message elements, keep welcome screen
            $('#lexai-messages-container').children('.lexai-message').remove();
            showWelcomeScreen();
            showToast('info', 'Chat limpiado', 'La conversación ha sido limpiada');
        }
    }

    /**
     * Scroll to bottom of messages
     */
    function scrollToBottom() {
        const messagesArea = $('#lexai-messages-area')[0];
        if (messagesArea) {
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }
    }

    /**
     * Escape HTML
     */
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Setup file handling
     */
    function setupFileHandling() {
        // File input change handler is already set up in initEventListeners
    }

    /**
     * Handle file selection
     */
    function handleFileSelection(e) {
        const files = e.target.files;
        if (!files || files.length === 0) return;
        
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            
            // Validate file
            if (!validateFile(file)) continue;
            
            // Add to preview
            addFilePreview(file);
        }
        
        // Clear input
        $(this).val('');
    }

    /**
     * Validate file
     */
    function validateFile(file) {
        const maxSize = 50 * 1024 * 1024; // 50MB
        const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];
        
        if (file.size > maxSize) {
            showToast('error', 'Archivo demasiado grande', `El archivo "${file.name}" excede el límite de 50MB`);
            return false;
        }
        
        if (!allowedTypes.includes(file.type)) {
            showToast('error', 'Tipo de archivo no permitido', `El archivo "${file.name}" no es un tipo permitido`);
            return false;
        }
        
        return true;
    }

    /**
     * Add file preview
     */
    function addFilePreview(file) {
        const fileId = 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const fileSize = formatFileSize(file.size);
        const fileIcon = getFileIcon(file.type);
        
        const previewHtml = `
            <div class="lexai-file-preview" data-file-id="${fileId}">
                <i class="${fileIcon}"></i>
                <div class="lexai-file-info">
                    <div class="lexai-file-name">${escapeHtml(file.name)}</div>
                    <div class="lexai-file-size">${fileSize}</div>
                </div>
                <button class="lexai-file-remove" onclick="removeFilePreview('${fileId}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        if (!filePreviewsVisible) {
            $('#lexai-file-preview-area').show();
            filePreviewsVisible = true;
        }
        
        $('#lexai-file-previews').append(previewHtml);
    }

    /**
     * Remove file preview
     */
    window.removeFilePreview = function(fileId) {
        $(`.lexai-file-preview[data-file-id="${fileId}"]`).remove();
        
        if ($('#lexai-file-previews').children().length === 0) {
            clearFilePreviews();
        }
    };

    /**
     * Clear file previews
     */
    function clearFilePreviews() {
        $('#lexai-file-preview-area').hide();
        $('#lexai-file-previews').empty();
        filePreviewsVisible = false;
    }

    /**
     * Format file size
     */
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Get file icon
     */
    function getFileIcon(mimeType) {
        switch (mimeType) {
            case 'application/pdf':
                return 'fas fa-file-pdf';
            case 'application/msword':
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return 'fas fa-file-word';
            case 'text/plain':
                return 'fas fa-file-alt';
            default:
                return 'fas fa-file';
        }
    }

    /**
     * Setup drag and drop
     */
    function setupDragAndDrop() {
        const dropZone = $('.lexai-input-wrapper')[0];
        
        if (!dropZone) return;
        
        dropZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('drag-over');
        });
        
        dropZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('drag-over');
        });
        
        dropZone.addEventListener('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('drag-over');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                // Simulate file input change
                const fileInput = $('#lexai-file-input')[0];
                fileInput.files = files;
                $(fileInput).trigger('change');
            }
        });
    }

    /**
     * Setup voice recording
     */
    function setupVoiceRecording() {
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            $('#lexai-voice-btn').hide();
            return;
        }
    }

    /**
     * Toggle voice recording
     */
    function toggleVoiceRecording() {
        if (isRecording) {
            stopRecording();
        } else {
            startRecording();
        }
    }

    /**
     * Start voice recording
     */
    function startRecording() {
        navigator.mediaDevices.getUserMedia({ audio: true })
            .then(function(stream) {
                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];
                
                mediaRecorder.ondataavailable = function(event) {
                    audioChunks.push(event.data);
                };
                
                mediaRecorder.onstop = function() {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    processAudioRecording(audioBlob);
                    
                    // Stop all tracks
                    stream.getTracks().forEach(track => track.stop());
                };
                
                mediaRecorder.start();
                isRecording = true;
                
                $('#lexai-voice-btn').addClass('recording');
                showToast('info', 'Grabando', 'Grabación de voz iniciada');
            })
            .catch(function(error) {
                console.error('Error accessing microphone:', error);
                showToast('error', 'Error de micrófono', 'No se pudo acceder al micrófono');
            });
    }

    /**
     * Stop voice recording
     */
    function stopRecording() {
        if (mediaRecorder && isRecording) {
            mediaRecorder.stop();
            isRecording = false;
            $('#lexai-voice-btn').removeClass('recording');
        }
    }

    /**
     * Process audio recording
     */
    function processAudioRecording(audioBlob) {
        const formData = new FormData();
        formData.append('action', 'lexai_process_stt');
        formData.append('nonce', lexaiConfig.nonce);
        formData.append('audio', audioBlob, 'audio.wav');

        showToast('info', 'Transcribiendo', 'Enviando audio para transcripción...');

        $.ajax({
            url: lexaiConfig.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success && response.data.text) {
                    $('#lexai-message-input').val(response.data.text);
                    handleInputChange();
                    showToast('success', 'Transcripción exitosa', 'Texto listo en el input.');
                } else {
                    showToast('error', 'Error de transcripción', response.data.message || 'No se pudo transcribir el audio.');
                }
            },
            error: function() {
                showToast('error', 'Error de red', 'Error al conectar con el servicio de transcripción.');
            }
        });
    }

    /**
     * Setup keyboard shortcuts
     */
    function setupKeyboardShortcuts() {
        $(document).on('keydown', function(e) {
            // Ctrl/Cmd + Enter to send message
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                sendMessage();
            }
            
            // Escape to close modals
            if (e.key === 'Escape') {
                closeModal();
                closeSidebar();
            }
            
            // Ctrl/Cmd + N for new chat
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                startNewChat();
            }
        });
    }

    /**
     * Load conversations
     */
    function loadConversations() {
        $.ajax({
            url: lexaiConfig.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lexai_load_conversations',
                nonce: lexaiConfig.nonce
            },
            success: function(response) {
                if (response.success) {
                    displayConversations(response.data);
                }
            },
            error: function() {
                console.error('Error loading conversations');
            }
        });
    }

    /**
     * Display conversations
     */
    function displayConversations(conversations) {
        const container = $('#lexai-conversations-list');
        container.empty();
        
        if (!conversations || conversations.length === 0) {
            container.append('<div class="lexai-no-conversations">No hay conversaciones</div>');
            return;
        }
        
        conversations.forEach(function(conversation) {
            const item = $(`
                <div class="lexai-conversation-item" data-id="${conversation.id}">
                    <div class="lexai-conversation-title">${escapeHtml(conversation.title || 'Conversación sin título')}</div>
                    <div class="lexai-conversation-date">${formatDate(conversation.updated_at)}</div>
                </div>
            `);
            
            item.on('click', function() {
                loadConversation(conversation.id);
            });
            
            container.append(item);
        });
    }

    /**
     * Load conversation
     */
    function loadConversation(conversationId) {
        $.ajax({
            url: lexaiConfig.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lexai_load_conversation',
                nonce: lexaiConfig.nonce,
                conversation_id: conversationId
            },
            success: function(response) {
                if (response.success) {
                    displayConversationMessages(response.data);
                    currentConversationId = conversationId;
                    
                    // Update active conversation
                    $('.lexai-conversation-item').removeClass('active');
                    $(`.lexai-conversation-item[data-id="${conversationId}"]`).addClass('active');
                    
                    // Close sidebar on mobile
                    if (window.innerWidth <= 768) {
                        closeSidebar();
                    }
                }
            },
            error: function() {
                showToast('error', 'Error', 'No se pudo cargar la conversación');
            }
        });
    }

    /**
     * Display conversation messages
     */
    function displayConversationMessages(data) {
        const container = $('#lexai-messages-container');
        // Clear only message elements, preserve welcome screen
        container.children('.lexai-message').remove();

        if (data.messages && data.messages.length > 0) {
            hideWelcomeScreen();

            data.messages.forEach(function(message) {
                const metadata = JSON.parse(message.metadata || '{}');
                const audioUrl = metadata.audio_url || null;
                addMessage(message.role, message.content, formatTime(message.created_at), audioUrl);
            });
        } else {
            showWelcomeScreen();
        }

        // Update title
        $('#lexai-current-title').text(data.title || 'Conversación');
    }

    /**
     * Update conversations list
     */
    function updateConversationsList() {
        // Reload conversations to reflect changes
        loadConversations();
    }

    /**
     * Check for existing messages
     */
    function checkForExistingMessages() {
        // Count only actual message elements, not welcome screen or typing indicator
        const messageElements = $('#lexai-messages-container').children('.lexai-message');
        if (messageElements.length > 0) {
            hideWelcomeScreen();
        } else {
            // Ensure welcome screen is visible if no real messages
            showWelcomeScreen();
        }
    }

    /**
     * Format date
     */
    function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) {
            return 'Hoy';
        } else if (diffDays === 2) {
            return 'Ayer';
        } else if (diffDays <= 7) {
            return `Hace ${diffDays} días`;
        } else {
            return date.toLocaleDateString();
        }
    }

    /**
     * Format time
     */
    function formatTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleTimeString();
    }

    /**
     * Show toast notification
     */
    function showToast(type, title, message, duration = 5000) {
        const toastId = 'toast_' + Date.now();
        const iconClass = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        }[type] || 'fas fa-info-circle';
        
        const toastHtml = `
            <div class="lexai-toast lexai-toast-${type}" id="${toastId}">
                <div class="lexai-toast-icon">
                    <i class="${iconClass}"></i>
                </div>
                <div class="lexai-toast-content">
                    <div class="lexai-toast-title">${escapeHtml(title)}</div>
                    <div class="lexai-toast-message">${escapeHtml(message)}</div>
                </div>
                <button class="lexai-toast-close" onclick="closeToast('${toastId}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        $('#lexai-toast-container').append(toastHtml);
        
        // Show toast
        setTimeout(() => {
            $(`#${toastId}`).addClass('show');
        }, 100);
        
        // Auto-hide toast
        setTimeout(() => {
            closeToast(toastId);
        }, duration);
    }

    /**
     * Close toast
     */
    window.closeToast = function(toastId) {
        const toast = $(`#${toastId}`);
        toast.removeClass('show');
        setTimeout(() => {
            toast.remove();
        }, 300);
    };

    /**
     * Toggle user menu
     */
    function toggleUserMenu() {
        $('#lexai-user-menu-dropdown').toggle();
    }

    /**
     * Hide user menu
     */
    function hideUserMenu() {
        $('#lexai-user-menu-dropdown').hide();
    }

    /**
     * Show modal
     */
    function showModal(modalId) {
        $(`#${modalId}`).addClass('show');
    }

    /**
     * Close modal
     */
    function closeModal() {
        $('.lexai-modal').removeClass('show');
    }

    /**
     * Show export modal
     */
    function showExportModal() {
        showModal('lexai-export-modal');
    }

    /**
     * Show settings modal
     */
    function showSettingsModal() {
        showModal('lexai-settings-modal');
    }

    /**
     * Show profile modal
     */
    function showProfileModal() {
        showModal('lexai-profile-modal');
        hideUserMenu();
    }

    /**
     * Show subscription modal
     */
    function showSubscriptionModal() {
        showModal('lexai-subscription-modal');
        hideUserMenu();
    }

    /**
     * Handle export
     */
    function handleExport() {
        const format = $(this).data('format');
        
        if (!currentConversationId) {
            showToast('warning', 'Sin conversación', 'No hay conversación para exportar');
            return;
        }
        
        // Show loading state
        $(this).addClass('lexai-loading');
        
        $.ajax({
            url: lexaiConfig.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lexai_export_conversation',
                nonce: lexaiConfig.nonce,
                conversation_id: currentConversationId,
                format: format
            },
            success: function(response) {
                if (response.success) {
                    // Trigger download
                    const link = document.createElement('a');
                    link.href = response.data.download_url;
                    link.download = response.data.filename;
                    link.click();
                    
                    showToast('success', 'Exportado', 'Conversación exportada correctamente');
                    closeModal();
                } else {
                    showToast('error', 'Error de exportación', response.data?.message || 'Error al exportar');
                }
            },
            error: function() {
                showToast('error', 'Error', 'Error al exportar la conversación');
            },
            complete: function() {
                $('.lexai-export-btn').removeClass('lexai-loading');
            }
        });
    }

    /**
     * Copy message
     */
    window.copyMessage = function(button) {
        const messageText = $(button).closest('.lexai-message-content').find('.lexai-message-text').text();
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(messageText).then(function() {
                showToast('success', 'Copiado', 'Mensaje copiado al portapapeles');
            });
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = messageText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showToast('success', 'Copiado', 'Mensaje copiado al portapapeles');
        }
    };

    /**
     * Regenerate response
     */
    window.regenerateResponse = function(button) {
        showToast('info', 'Regenerando', 'Funcionalidad en desarrollo');
    };

    /**
     * Retry last message
     */
    window.retryLastMessage = function() {
        showToast('info', 'Reintentando', 'Funcionalidad en desarrollo');
    };

    // Global variable for tracking last message ID
    let globalLastMessageId = 0;

    /**
     * Format message content (render markdown)
     */
    function formatMessage(content) {
        if (!content) return '';

        // Use marked.js for robust Markdown rendering
        // Ensure marked.min.js is loaded before this script
        if (typeof marked !== 'undefined') {
            return marked.parse(content);
        } else {
            // Fallback to simple markdown renderer if marked.js is not available
            return content
                // Headers
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')

                // Bold and italic
                .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/gim, '<em>$1</em>')

                // Code blocks
                .replace(/```([^`]*?)```/gim, '<pre><code>$1</code></pre>')
                .replace(/`([^`]*?)`/gim, '<code>$1</code>')

                // Lists
                .replace(/^[\s]*[-\*\+] (.*$)/gim, '<li>$1</li>')
                .replace(/^[\s]*\d+\. (.*$)/gim, '<li>$1</li>')

                // Blockquotes
                .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')

                // Links
                .replace(/\[([^\]]*)\]\(([^)]*)\)/gim, '<a href="$2" target="_blank">$1</a>')

                // Line breaks
                .replace(/\n\n/gim, '</p><p>')
                .replace(/\n/gim, '<br>')

                // Wrap in paragraph if not already wrapped
                .replace(/^(?!<[h1-6]|<p|<ul|<ol|<li|<blockquote|<pre)/gim, '<p>')
                .replace(/(?<!<\/[h1-6]>|<\/p>|<\/ul>|<\/ol>|<\/li>|<\/blockquote>|<\/pre>)$/gim, '</p>');
        }
    }

    /**
     * Format time for display
     */
    function formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('es-ES', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Show typing indicator
     */
    function showTypingIndicator() {
        // Remove existing typing indicator
        hideTypingIndicator();

        const typingHtml = `
            <div id="lexai-typing-indicator" class="lexai-message lexai-message-assistant">
                <div class="lexai-message-avatar">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <div class="lexai-message-content">
                    <div class="lexai-typing-animation">
                        <div class="lexai-typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <div class="lexai-typing-text">Los agentes están trabajando...</div>
                    </div>
                </div>
            </div>
        `;

        $('#lexai-messages').append(typingHtml);
        scrollToBottom();
    }

    /**
     * Hide typing indicator
     */
    function hideTypingIndicator() {
        $('#lexai-typing-indicator').remove();
    }

    /**
     * Poll for status updates (new architecture)
     */
    function pollForPartialMessages() {
        if (!currentConversationId) return;

        $.ajax({
            url: lexaiConfig.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lexai_get_status',
                nonce: lexaiConfig.nonce,
                conversation_id: currentConversationId,
                last_message_id: globalLastMessageId
            },
            timeout: 10000,
            success: function(response) {
                if (response.success && response.data.messages && response.data.messages.length > 0) {
                    response.data.messages.forEach(function(message) {
                        // Add each partial message to the chat
                        addPartialMessage(message);

                        // Update last message ID
                        if (message.id > globalLastMessageId) {
                            globalLastMessageId = message.id;
                        }
                    });

                    // If we received the final response, stop polling and hide typing indicator
                    if (response.data.has_final_response) {
                        pollingActive = false;
                        if (pollingInterval) {
                            clearInterval(pollingInterval);
                        }
                        hideTypingIndicator();
                        setProcessingState(false);
                        console.log('Final response received, stopping polling');
                    }
                }
            },
            error: function(xhr, status, error) {
                console.log('Polling error:', error);
                // Continue polling even on errors, but with exponential backoff
                if (pollingInterval) {
                    clearInterval(pollingInterval);
                    pollingInterval = setInterval(pollForPartialMessages, 5000); // Slower polling on errors
                }
            }
        });
    }

    /**
     * Add partial message to chat with special styling
     */
    function addPartialMessage(message) {
        // Check if message already exists to avoid duplicates
        if ($(`[data-message-id="${message.id}"]`).length > 0) {
            return;
        }

        let messageClass = 'lexai-partial-message';
        let icon = '💬';

        // Different styling based on message type
        switch (message.type) {
            case 'plan':
                messageClass += ' lexai-plan-message';
                icon = '📋';
                break;
            case 'task_start':
                messageClass += ' lexai-task-start';
                icon = '🔄';
                break;
            case 'agent_assigned':
                messageClass += ' lexai-agent-assigned';
                icon = '👤';
                break;
            case 'task_result':
                messageClass += ' lexai-task-result';
                icon = '✅';
                break;
            case 'task_error':
                messageClass += ' lexai-task-error';
                icon = '❌';
                break;
            case 'final_response':
                messageClass += ' lexai-final-response';
                icon = '🎯';
                break;
        }

        const messageHtml = `
            <div class="lexai-message assistant ${messageClass}" data-message-id="${message.id}">
                <div class="lexai-message-content">
                    <div class="lexai-message-header">
                        <span class="lexai-message-icon">${icon}</span>
                        <span class="lexai-message-time">${formatTime(message.timestamp)}</span>
                    </div>
                    <div class="lexai-message-text">${formatMessage(message.content)}</div>
                </div>
            </div>
        `;

        $('#lexai-messages').append(messageHtml);
        scrollToBottom();
    }

    /**
     * Handle window resize
     */
    $(window).on('resize', function() {
        if (window.innerWidth > 768 && sidebarOpen) {
            closeSidebar();
        }
    });

    /**
     * Load user stats asynchronously
     */
    function loadUserStatsAsync() {
        $.ajax({
            url: lexaiConfig.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lexai_get_user_stats',
                nonce: lexaiConfig.nonce
            },
            timeout: 10000,
            success: function(response) {
                if (response.success) {
                    // Update config with real stats
                    lexaiConfig.userRole = response.data.userRole;
                    lexaiConfig.usageStats = response.data.usageStats;

                    // Update UI if needed
                    updateUsageDisplay();
                }
            },
            error: function() {
                console.log('Failed to load user stats - using defaults');
            }
        });
    }

    /**
     * Update usage display in UI
     */
    function updateUsageDisplay() {
        // Update usage stats in sidebar if visible
        const usageElement = $('.lexai-usage-stats');
        if (usageElement.length && lexaiConfig.usageStats) {
            const stats = lexaiConfig.usageStats;
            if (stats.limit > 0) {
                usageElement.html(`${stats.used}/${stats.limit} consultas utilizadas`);
            } else {
                usageElement.html('Consultas ilimitadas');
            }
        }
    }

    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        // Debug authentication
        console.log('=== LexAI Authentication Debug ===');
        console.log('lexaiConfig:', lexaiConfig);
        console.log('User ID:', lexaiConfig.userId);
        console.log('Nonce:', lexaiConfig.nonce);

        // Check if user is authenticated
        if (!lexaiConfig.userId || lexaiConfig.userId === '0') {
            console.error('❌ User not authenticated');
            showErrorMessage('Error de autenticación. Por favor, inicia sesión.');
            return;
        }

        if (!lexaiConfig.nonce) {
            console.error('❌ No nonce provided');
            showErrorMessage('Error de seguridad. Por favor, recarga la página.');
            return;
        }

        console.log('✅ Authentication OK, initializing...');
        initLexAI();
    });

})(jQuery);