<?php
/**
 * LexAI TTS Handler Class
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class LexAI_TTS_Handler {

    private $api_handler;

    public function __construct() {
        $this->api_handler = new LexAI_API_Handler();
    }

    /**
     * Generates audio from text using Gemini TTS API.
     *
     * @param string $text The text to convert to speech.
     * @param int $user_id The ID of the user requesting TTS.
     * @param int $message_id The ID of the message being converted (for logging).
     * @return array { 'success': bool, 'audio_url': string, 'error': string }
     */
    public function generate_audio($text, $user_id, $message_id = null) {
        try {
            // Get active Gemini API key
            $api_key_data = $this->api_handler->get_active_api_key('gemini');
            if (!$api_key_data || empty($api_key_data->api_key)) {
                throw new Exception(__('Gemini API key not configured.', 'lexai'));
            }
            $gemini_api_key = $api_key_data->api_key;

            // Prepare the request to Gemini TTS API
            $url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-tts:generateContent?key=" . $gemini_api_key;
            $headers = [
                'Content-Type: application/json',
            ];

            // Choose a voice. You can make this configurable later.
            $voice_name = 'Kore'; // Example voice from SPEECH GENERATION.md

            $body = [
                "contents" => [
                    ["parts" => [["text" => $text]]]
                ],
                "generationConfig" => [
                    "responseModalities" => ["AUDIO"],
                    "speechConfig" => [
                        "voiceConfig" => [
                            "prebuiltVoiceConfig" => [
                                "voiceName" => $voice_name
                            ]
                        ]
                    ]
                ],
                "model" => "gemini-2.5-flash-preview-tts",
            ];

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60); // Increased timeout for API call

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($response === false) {
                throw new Exception(__('cURL Error: ' . $error, 'lexai'));
            }

            $response_data = json_decode($response, true);

            if ($http_code !== 200 || !isset($response_data['candidates'][0]['content']['parts'][0]['inlineData']['data'])) {
                $error_message = isset($response_data['error']['message']) ? $response_data['error']['message'] : __('Unknown API error.', 'lexai');
                throw new Exception(__('Gemini TTS API Error: ' . $error_message, 'lexai'));
            }

            $audio_base64 = $response_data['candidates'][0]['content']['parts'][0]['inlineData']['data'];
            $audio_data = base64_decode($audio_base64);

            // Save audio to a temporary file
            $upload_dir = wp_upload_dir();
            $tts_dir = $upload_dir['basedir'] . '/lexai_tts';
            if (!file_exists($tts_dir)) {
                wp_mkdir_p($tts_dir);
            }

            $filename = 'lexai_tts_' . md5($text) . '_' . time() . '.wav'; // Using WAV for simplicity
            $file_path = $tts_dir . '/' . $filename;
            file_put_contents($file_path, $audio_data);

            // Return URL
            return [
                'success' => true,
                'audio_url' => $upload_dir['baseurl'] . '/lexai_tts/' . $filename
            ];

        } catch (Exception $e) {
            error_log('LexAI TTS Generation Error: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}