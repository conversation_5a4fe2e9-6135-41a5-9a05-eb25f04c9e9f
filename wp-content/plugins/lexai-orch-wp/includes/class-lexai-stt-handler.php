<?php
/**
 * LexAI STT Handler Class
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class LexAI_STT_Handler {

    private $api_handler;

    public function __construct() {
        $this->api_handler = new LexAI_API_Handler();
    }

    /**
     * Transcribes audio to text using Gemini STT API.
     *
     * @param array $audio_file The uploaded audio file data (from $_FILES).
     * @param int $user_id The ID of the user requesting STT.
     * @return array { 'success': bool, 'text': string, 'error': string }
     */
    public function transcribe_audio($audio_file, $user_id) {
        try {
            // Get active Gemini API key
            $api_key_data = $this->api_handler->get_active_api_key('gemini');
            if (!$api_key_data || empty($api_key_data->api_key)) {
                throw new Exception(__('Gemini API key not configured.', 'lexai'));
            }
            $gemini_api_key = $api_key_data->api_key;

            // Read audio file content
            $audio_content = file_get_contents($audio_file['tmp_name']);
            if ($audio_content === false) {
                throw new Exception(__('Failed to read audio file.', 'lexai'));
            }

            // Encode audio to base64
            $audio_base64 = base64_encode($audio_content);

            // Prepare the request to Gemini STT API (using a suitable model for STT)
            // Note: Gemini's direct STT capabilities might be part of a broader model or a specific endpoint.
            // For this example, we'll assume a generic content generation endpoint that can handle audio input.
            // In a real scenario, you might need a dedicated STT API or a model specifically fine-tuned for STT.
            $url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=" . $gemini_api_key;
            $headers = [
                'Content-Type: application/json',
            ];

            $body = [
                "contents" => [
                    [
                        "parts" => [
                            ["inlineData" => [
                                "mimeType" => $audio_file['type'],
                                "data" => $audio_base64
                            ]],
                            ["text" => "Transcribe the audio to text."] // Instruction for the model
                        ]
                    ]
                ]
            ];

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60); // Increased timeout for API call

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($response === false) {
                throw new Exception(__('cURL Error: ' . $error, 'lexai'));
            }

            $response_data = json_decode($response, true);

            if ($http_code !== 200 || !isset($response_data['candidates'][0]['content']['parts'][0]['text'])) {
                $error_message = isset($response_data['error']['message']) ? $response_data['error']['message'] : __('Unknown API error.', 'lexai');
                throw new Exception(__('Gemini STT API Error: ' . $error_message, 'lexai'));
            }

            $transcribed_text = $response_data['candidates'][0]['content']['parts'][0]['text'];

            return ['success' => true, 'text' => $transcribed_text];

        } catch (Exception $e) {
            error_log('LexAI STT Transcription Error: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
