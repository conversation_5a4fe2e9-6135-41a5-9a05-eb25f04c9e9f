<?php
/**
 * Plugin Activation Handler
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * LexAI Activator Class
 */
class LexAI_Activator {
    
    /**
     * Plugin activation handler
     */
    public static function activate() {
        self::check_required_extensions();
        self::create_database_tables();
        self::create_default_settings();
        self::create_default_orchestrator_agent();
        self::schedule_cleanup_events();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Check for required PHP extensions.
     * Displays admin notices if any are missing.
     */
    private static function check_required_extensions() {
        $required_extensions = [
            'json' => 'JSON',
            'mbstring' => 'Multibyte String (mbstring)',
            'openssl' => 'OpenSSL',
            'curl' => 'cURL',
            'zip' => 'ZipArchive (para DOCX)',
            'fileinfo' => 'Fileinfo (para detección de MIME types)',
            'gd' => 'GD (recomendado para PhpWord)',
        ];

        $missing_extensions = [];
        foreach ($required_extensions as $ext_name => $ext_description) {
            if (!extension_loaded($ext_name)) {
                $missing_extensions[] = $ext_description;
            }
        }

        if (!empty($missing_extensions)) {
            $message = sprintf(
                __('LexAI requiere las siguientes extensiones de PHP que no están instaladas o habilitadas: %s. Por favor, contacta a tu proveedor de hosting para habilitarlas.', 'lexai'),
                implode(', ', $missing_extensions)
            );
            // Use a transient to display the admin notice after redirect
            set_transient('lexai_activation_error', $message, 5 * MINUTE_IN_SECONDS);

            // Deactivate the plugin if critical extensions are missing
            deactivate_plugins(plugin_basename(LEXAI_PLUGIN_FILE));

            // Prevent further execution
            wp_die($message, __('Error de Activación de Plugin', 'lexai'), array('back_link' => true));
        }
    }
    
    /**
     * Create database tables
     */
    private static function create_database_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Agents table
        $agents_table = $wpdb->prefix . LEXAI_AGENTS_TABLE;
        $agents_sql = "CREATE TABLE $agents_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            system_instruction longtext NOT NULL,
            tools json DEFAULT NULL,
            model varchar(100) DEFAULT 'gemini-2.5-flash',
            max_output_tokens int(11) DEFAULT 8192,
            status enum('active','inactive') DEFAULT 'active',
            created_by bigint(20) unsigned NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY created_by (created_by),
            KEY status (status),
            KEY model (model)
        ) $charset_collate;";
        
        // Conversations table
        $conversations_table = $wpdb->prefix . LEXAI_CONVERSATIONS_TABLE;
        $conversations_sql = "CREATE TABLE $conversations_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL,
            title varchar(255) DEFAULT NULL,
            status enum('active','archived','deleted') DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY status (status),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        // Messages table
        $messages_table = $wpdb->prefix . LEXAI_MESSAGES_TABLE;
        $messages_sql = "CREATE TABLE $messages_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            conversation_id bigint(20) unsigned NOT NULL,
            user_id bigint(20) unsigned NOT NULL,
            role enum('user','assistant','system') NOT NULL,
            content longtext NOT NULL,
            metadata json DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY conversation_id (conversation_id),
            KEY user_id (user_id),
            KEY role (role),
            KEY created_at (created_at),
            FOREIGN KEY (conversation_id) REFERENCES $conversations_table(id) ON DELETE CASCADE
        ) $charset_collate;";
        
        // API Keys table (UPDATED FOR SECURITY)
        $api_keys_table = $wpdb->prefix . LEXAI_API_KEYS_TABLE;
        $api_keys_sql = "CREATE TABLE $api_keys_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            encrypted_api_key text NOT NULL,
            provider enum('gemini','openai') DEFAULT 'gemini',
            status enum('active','inactive','error') DEFAULT 'active',
            usage_count bigint(20) unsigned DEFAULT 0,
            last_used datetime DEFAULT NULL,
            rate_limit_remaining int DEFAULT NULL,
            rate_limit_reset datetime DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY provider (provider),
            KEY status (status),
            KEY last_used (last_used)
        ) $charset_collate;";
        
        // Usage logs table
        $usage_logs_table = $wpdb->prefix . LEXAI_USAGE_LOGS_TABLE;
        $usage_logs_sql = "CREATE TABLE $usage_logs_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL,
            conversation_id bigint(20) unsigned DEFAULT NULL,
            action_type enum('message','search','document_upload') NOT NULL,
            tokens_used int DEFAULT 0,
            api_key_id bigint(20) unsigned DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY conversation_id (conversation_id),
            KEY action_type (action_type),
            KEY created_at (created_at),
            KEY api_key_id (api_key_id)
        ) $charset_collate;";
        
        // Tasks table (for orchestrator task management)
        $tasks_table = $wpdb->prefix . LEXAI_TASKS_TABLE;
        $tasks_sql = "CREATE TABLE $tasks_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            conversation_id bigint(20) unsigned NOT NULL,
            parent_task_id bigint(20) unsigned DEFAULT NULL,
            agent_id bigint(20) unsigned DEFAULT NULL,
            task_type varchar(100) NOT NULL,
            description text,
            input_data json DEFAULT NULL,
            status enum('pending','in_progress','completed','failed','cancelled') DEFAULT 'pending',
            priority int DEFAULT 5,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            started_at datetime DEFAULT NULL,
            completed_at datetime DEFAULT NULL,
            PRIMARY KEY (id),
            KEY conversation_id (conversation_id),
            KEY parent_task_id (parent_task_id),
            KEY agent_id (agent_id),
            KEY status (status),
            KEY priority (priority),
            FOREIGN KEY (conversation_id) REFERENCES $conversations_table(id) ON DELETE CASCADE
        ) $charset_collate;";
        
        // Task executions table (for tracking task execution details)
        $task_executions_table = $wpdb->prefix . LEXAI_TASK_EXECUTIONS_TABLE;
        $task_executions_sql = "CREATE TABLE $task_executions_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            task_id bigint(20) unsigned NOT NULL,
            agent_id bigint(20) unsigned DEFAULT NULL,
            execution_data json DEFAULT NULL,
            result_data json DEFAULT NULL,
            error_message text DEFAULT NULL,
            execution_time_ms int DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY task_id (task_id),
            KEY agent_id (agent_id),
            KEY created_at (created_at),
            FOREIGN KEY (task_id) REFERENCES $tasks_table(id) ON DELETE CASCADE
        ) $charset_collate;";

        // Chat Tasks table (for polling mechanism)
        $chat_tasks_table = $wpdb->prefix . LEXAI_CHAT_TASKS_TABLE;
        $chat_tasks_sql = "CREATE TABLE $chat_tasks_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            conversation_id bigint(20) unsigned NOT NULL,
            initial_prompt longtext NOT NULL,
            status enum('pending','in_progress','completed','failed') DEFAULT 'pending',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY conversation_id (conversation_id),
            KEY status (status)
        ) $charset_collate;";

        // Files table (for file uploads and document understanding)
        $files_table = $wpdb->prefix . 'lexai_files';
        $files_sql = "CREATE TABLE $files_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            conversation_id bigint(20) unsigned DEFAULT NULL,
            user_id bigint(20) unsigned NOT NULL,
            original_filename varchar(255) NOT NULL,
            stored_filename varchar(255) NOT NULL,
            file_path varchar(500) NOT NULL,
            file_type varchar(100) NOT NULL,
            file_size bigint(20) unsigned NOT NULL,
            mime_type varchar(100) NOT NULL,
            extracted_text longtext DEFAULT NULL,
            file_hash varchar(64) NOT NULL,
            gemini_uri varchar(500) DEFAULT NULL,
            processing_status enum('pending','processing','completed','failed') DEFAULT 'pending',
            processing_error text DEFAULT NULL,
            expires_at datetime NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY conversation_id (conversation_id),
            KEY user_id (user_id),
            KEY file_hash (file_hash),
            KEY expires_at (expires_at),
            KEY processing_status (processing_status),
            FOREIGN KEY (conversation_id) REFERENCES $conversations_table(id) ON DELETE SET NULL,
            FOREIGN KEY (user_id) REFERENCES {$wpdb->users}(ID) ON DELETE CASCADE
        ) $charset_collate;";

        // Tools table - stores available tools/functions
        $tools_table = $wpdb->prefix . LEXAI_TOOLS_TABLE;
        $tools_sql = "CREATE TABLE $tools_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(100) NOT NULL,
            display_name varchar(200) NOT NULL,
            description text,
            category varchar(50) DEFAULT 'general',
            schema_definition longtext,
            is_enabled tinyint(1) DEFAULT 1,
            is_system tinyint(1) DEFAULT 0,
            configuration longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY name (name),
            KEY is_enabled (is_enabled),
            KEY category (category)
        ) $charset_collate;";

        // Agent tools table - stores which tools each agent can use
        $agent_tools_table = $wpdb->prefix . LEXAI_AGENT_TOOLS_TABLE;
        $agent_tools_sql = "CREATE TABLE $agent_tools_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            agent_id bigint(20) unsigned NOT NULL,
            tool_id int(11) NOT NULL,
            is_enabled tinyint(1) DEFAULT 1,
            configuration longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY agent_tool (agent_id, tool_id),
            KEY agent_id (agent_id),
            KEY tool_id (tool_id),
            FOREIGN KEY (agent_id) REFERENCES $agents_table(id) ON DELETE CASCADE,
            FOREIGN KEY (tool_id) REFERENCES $tools_table(id) ON DELETE CASCADE
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($agents_sql);
        dbDelta($conversations_sql);
        dbDelta($messages_sql);
        dbDelta($api_keys_sql);
        dbDelta($usage_logs_sql);
        dbDelta($tasks_sql);
        dbDelta($task_executions_sql);
        dbDelta($chat_tasks_sql);
        dbDelta($files_sql);
        dbDelta($tools_sql);
        dbDelta($agent_tools_sql);

        // Initialize default tools
        self::initialize_default_tools();

        // SECURITY: Migration moved to check_for_updates() in main plugin class
        // This ensures existing users get the migration when updating

        // Schedule file cleanup
        if (!wp_next_scheduled('lexai_cleanup_files')) {
            wp_schedule_event(time(), 'daily', 'lexai_cleanup_files');
        }
    }
    
    /**
     * Create default plugin settings
     */
    private static function create_default_settings() {
        $default_settings = array(
            'general' => array(
                'enable_validator_agent' => false,
                'max_conversation_history' => 50,
                'session_timeout' => 3600, // 1 hour
                'enable_logging' => true
            ),
            'pinecone' => array(
                'api_key' => '',
                'environment' => '',
                'index_name' => 'lexai-knowledge-base'
            ),
            'usage_limits' => array(
                'administrator' => array('daily_messages' => -1, 'monthly_messages' => -1),
                'editor' => array('daily_messages' => 500, 'monthly_messages' => 5000),
                'author' => array('daily_messages' => 200, 'monthly_messages' => 2000),
                'contributor' => array('daily_messages' => 100, 'monthly_messages' => 1000),
                'subscriber' => array('daily_messages' => 20, 'monthly_messages' => 200)
            )
        );
        
        update_option('lexai_settings', $default_settings);
    }
    
    /**
     * Create default system agents (Orchestrator, Validator and Legal Specialists)
     */
    private static function create_default_orchestrator_agent() {
        global $wpdb;
        
        $agents_table = $wpdb->prefix . LEXAI_AGENTS_TABLE;
        
        // Check if agents already exist
        $existing_orchestrator = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $agents_table WHERE name = %s",
            'Orquestador Principal'
        ));
        
        $existing_validator = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $agents_table WHERE name = %s",
            'Agente Validador'
        ));
        
        // Create Orchestrator Agent if it doesn't exist
        if (!$existing_orchestrator) {
            $orchestrator_instruction = "Eres el Agente Orquestador de LexAI, especializado en derecho mexicano. Tu función principal es:

1. ANÁLISIS DE CONSULTAS: Analiza cada consulta legal para identificar:
   - Área del derecho mexicano involucrada (civil, penal, laboral, fiscal, constitucional, etc.)
   - Complejidad de la consulta
   - Tipo de respuesta requerida (información, análisis, redacción, etc.)

2. SELECCIÓN DE AGENTES: Basándote en tu análisis, selecciona el equipo de agentes especializados más adecuado para resolver la consulta.

3. PLANIFICACIÓN DE TAREAS: Crea un plan de ejecución dividiendo la consulta en subtareas específicas y asignándolas a los agentes apropiados.

4. SÍNTESIS DE RESPUESTAS: Integra las respuestas de los agentes especializados en una respuesta coherente, completa y fundamentada en el marco legal mexicano.

IMPORTANTE: Siempre fundamenta tus respuestas en la legislación mexicana vigente, jurisprudencia de la SCJN y criterios del TFJA cuando sea aplicable. Cita siempre los artículos y tesis relevantes.";

            $wpdb->insert(
                $agents_table,
                array(
                    'name' => 'Orquestador Principal',
                    'description' => 'Agente central que coordina y orquesta las tareas entre los agentes especializados',
                    'system_instruction' => $orchestrator_instruction,
                    'tools' => json_encode(array('legal_knowledge_base', 'google_search')),
                    'status' => 'active',
                    'created_by' => 1
                ),
                array('%s', '%s', '%s', '%s', '%s', '%d')
            );
        }
        
        // Create Validator Agent if it doesn't exist
        if (!$existing_validator) {
            $validator_instruction = "Eres el Agente Validador de LexAI, especializado en verificar la precisión y calidad de las respuestas legales. Tu función es:

1. VALIDACIÓN DE CONTENIDO LEGAL:
   - Verificar que las citas legales sean correctas y estén vigentes
   - Confirmar que los artículos mencionados existan y sean aplicables
   - Validar que la jurisprudencia citada sea auténtica y relevante

2. CONTROL DE CALIDAD:
   - Revisar la coherencia y lógica de las respuestas
   - Asegurar que el lenguaje sea claro y profesional
   - Verificar que se respondan todas las partes de la consulta

3. VERIFICACIÓN DE FUENTES:
   - Confirmar que las fuentes citadas sean confiables y oficiales
   - Validar que las referencias a leyes y códigos sean precisas
   - Asegurar que las fechas y vigencias sean correctas

4. MEJORA CONTINUA:
   - Sugerir mejoras en las respuestas cuando sea necesario
   - Identificar áreas donde se requiera información adicional
   - Recomendar actualizaciones cuando la legislación cambie

IMPORTANTE: Tu función es crítica para mantener la calidad y confiabilidad del sistema. Siempre prioriza la precisión sobre la velocidad.";

            $wpdb->insert(
                $agents_table,
                array(
                    'name' => 'Agente Validador',
                    'description' => 'Agente especializado en validar la precisión y calidad de las respuestas legales',
                    'system_instruction' => $validator_instruction,
                    'tools' => json_encode(array('legal_knowledge_base', 'google_search', 'jurisprudence_search')),
                    'status' => 'active',
                    'created_by' => 1
                ),
                array('%s', '%s', '%s', '%s', '%s', '%d')
            );
        }

        // Create specialized legal agents
        self::create_specialized_agents();
    }

    /**
     * Create specialized legal agents based on templates
     */
    private static function create_specialized_agents() {
        global $wpdb;

        $agents_table = $wpdb->prefix . LEXAI_AGENTS_TABLE;

        // Get agent templates from factory
        require_once LEXAI_PLUGIN_DIR . 'includes/class-lexai-agent-factory.php';
        $agent_factory = new LexAI_Agent_Factory();
        $templates = $agent_factory->get_agent_templates();

        foreach ($templates as $template_key => $template) {
            // Check if agent already exists
            $existing_agent = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $agents_table WHERE name = %s",
                $template['name']
            ));

            if (!$existing_agent) {
                $wpdb->insert(
                    $agents_table,
                    array(
                        'name' => $template['name'],
                        'description' => $template['description'],
                        'system_instruction' => $template['system_instruction'],
                        'tools' => json_encode($template['tools']),
                        'model' => $template['model'],
                        'status' => 'active',
                        'created_by' => 1
                    ),
                    array('%s', '%s', '%s', '%s', '%s', '%s', '%d')
                );

                error_log("LexAI: Created specialized agent: " . $template['name']);
            }
        }
    }

    /**
     * Schedule cleanup events
     */
    private static function schedule_cleanup_events() {
        if (!wp_next_scheduled('lexai_daily_cleanup')) {
            wp_schedule_event(time(), 'daily', 'lexai_daily_cleanup');
        }
        
        if (!wp_next_scheduled('lexai_weekly_cleanup')) {
            wp_schedule_event(time(), 'weekly', 'lexai_weekly_cleanup');
        }
    }

    /**
     * Migrate existing API keys to encrypted format (SECURITY FIX)
     * Made public so it can be called from the main plugin class
     */
    public static function migrate_api_keys_to_encrypted() {
        global $wpdb;

        $api_keys_table = $wpdb->prefix . LEXAI_API_KEYS_TABLE;

        // First check if the table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$api_keys_table'");
        if (!$table_exists) {
            error_log('LexAI Migration: API keys table does not exist, skipping migration');
            return;
        }

        // Check if old column exists
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $api_keys_table LIKE 'api_key'");

        if (!empty($columns)) {
            // Get all existing API keys
            $existing_keys = $wpdb->get_results(
                "SELECT id, api_key FROM $api_keys_table WHERE api_key IS NOT NULL AND api_key != ''"
            );

            if (!empty($existing_keys)) {
                // Load encryption functions
                require_once LEXAI_PLUGIN_DIR . 'includes/class-lexai-api-handler.php';

                foreach ($existing_keys as $key_data) {
                    try {
                        // Create temporary API handler for encryption
                        $temp_handler = new class {
                            private function get_encryption_key() {
                                $salt_data = wp_salt('auth') . wp_salt('secure_auth') . wp_salt('logged_in') . wp_salt('nonce');
                                return hash('sha256', $salt_data, true);
                            }

                            public function encrypt_api_key($api_key) {
                                $encryption_key = $this->get_encryption_key();
                                $iv = openssl_random_pseudo_bytes(16);

                                $encrypted = openssl_encrypt(
                                    $api_key,
                                    'AES-256-CBC',
                                    $encryption_key,
                                    OPENSSL_RAW_DATA,
                                    $iv
                                );

                                if ($encrypted === false) {
                                    throw new Exception('Error al encriptar la clave API');
                                }

                                return base64_encode($iv . $encrypted);
                            }
                        };

                        // Encrypt the existing key
                        $encrypted_key = $temp_handler->encrypt_api_key($key_data->api_key);

                        // Update with encrypted version
                        $wpdb->update(
                            $api_keys_table,
                            array('encrypted_api_key' => $encrypted_key),
                            array('id' => $key_data->id),
                            array('%s'),
                            array('%d')
                        );

                    } catch (Exception $e) {
                        error_log('LexAI Migration Error: Failed to encrypt API key ID ' . $key_data->id . ': ' . $e->getMessage());
                    }
                }

                // Drop old column after migration
                $wpdb->query("ALTER TABLE $api_keys_table DROP COLUMN api_key");

                error_log('LexAI: Successfully migrated ' . count($existing_keys) . ' API keys to encrypted format');
            }
        }
    }

    /**
     * Initialize default tools
     */
    private static function initialize_default_tools() {
        global $wpdb;

        $tools_table = $wpdb->prefix . LEXAI_TOOLS_TABLE;

        // Check if tools already exist
        $existing_tools = $wpdb->get_var("SELECT COUNT(*) FROM $tools_table");
        if ($existing_tools > 0) {
            return; // Tools already initialized
        }

        $default_tools = array(
            array(
                'name' => 'legal_knowledge_base',
                'display_name' => 'Base de Conocimiento Legal',
                'description' => 'Busca información en la base de conocimiento legal usando vectores semánticos.',
                'category' => 'knowledge',
                'schema_definition' => json_encode(array(
                    'name' => 'legal_knowledge_base',
                    'description' => 'Busca información relevante en la base de conocimiento legal',
                    'parameters' => array(
                        'type' => 'OBJECT',
                        'properties' => array(
                            'query' => array(
                                'type' => 'STRING',
                                'description' => 'Consulta de búsqueda'
                            ),
                            'category' => array(
                                'type' => 'STRING',
                                'description' => 'Categoría legal específica (opcional)'
                            )
                        ),
                        'required' => array('query')
                    )
                )),
                'is_enabled' => 1,
                'is_system' => 1
            ),
            array(
                'name' => 'google_search',
                'display_name' => 'Búsqueda en Google',
                'description' => 'Realiza búsquedas en Google para obtener información actualizada.',
                'category' => 'search',
                'schema_definition' => json_encode(array(
                    'name' => 'google_search',
                    'description' => 'Busca información actualizada en Google',
                    'parameters' => array(
                        'type' => 'OBJECT',
                        'properties' => array(
                            'query' => array(
                                'type' => 'STRING',
                                'description' => 'Términos de búsqueda'
                            )
                        ),
                        'required' => array('query')
                    )
                )),
                'is_enabled' => 1,
                'is_system' => 0
            ),
            array(
                'name' => 'document_analysis',
                'display_name' => 'Análisis de Documentos',
                'description' => 'Analiza documentos legales para extraer información clave.',
                'category' => 'analysis',
                'schema_definition' => json_encode(array(
                    'name' => 'document_analysis',
                    'description' => 'Analiza documentos legales para extraer información clave',
                    'parameters' => array(
                        'type' => 'OBJECT',
                        'properties' => array(
                            'document_type' => array(
                                'type' => 'STRING',
                                'description' => 'Tipo de documento a analizar'
                            ),
                            'analysis_focus' => array(
                                'type' => 'STRING',
                                'description' => 'Enfoque específico del análisis'
                            )
                        ),
                        'required' => array('document_type')
                    )
                )),
                'is_enabled' => 0,
                'is_system' => 0
            ),
            array(
                'name' => 'contract_generation',
                'display_name' => 'Generación de Contratos',
                'description' => 'Genera contratos legales basados en plantillas.',
                'category' => 'generation',
                'schema_definition' => json_encode(array(
                    'name' => 'contract_generation',
                    'description' => 'Genera contratos legales basados en plantillas',
                    'parameters' => array(
                        'type' => 'OBJECT',
                        'properties' => array(
                            'contract_type' => array(
                                'type' => 'STRING',
                                'description' => 'Tipo de contrato a generar'
                            ),
                            'parties' => array(
                                'type' => 'STRING',
                                'description' => 'Información de las partes'
                            ),
                            'terms' => array(
                                'type' => 'STRING',
                                'description' => 'Términos específicos del contrato'
                            )
                        ),
                        'required' => array('contract_type', 'parties')
                    )
                )),
                'is_enabled' => 0,
                'is_system' => 0
            ),
            array(
                'name' => 'legal_research',
                'display_name' => 'Investigación Jurídica',
                'description' => 'Realiza investigación jurídica profunda sobre temas legales.',
                'category' => 'research',
                'schema_definition' => json_encode(array(
                    'name' => 'legal_research',
                    'description' => 'Realiza investigación jurídica profunda',
                    'parameters' => array(
                        'type' => 'OBJECT',
                        'properties' => array(
                            'topic' => array(
                                'type' => 'STRING',
                                'description' => 'Tema legal a investigar'
                            ),
                            'jurisdiction' => array(
                                'type' => 'STRING',
                                'description' => 'Jurisdicción aplicable'
                            ),
                            'legal_area' => array(
                                'type' => 'STRING',
                                'description' => 'Área del derecho'
                            )
                        ),
                        'required' => array('topic')
                    )
                )),
                'is_enabled' => 0,
                'is_system' => 0
            ),
            array(
                'name' => 'jurisprudence_search',
                'display_name' => 'Búsqueda de Jurisprudencia',
                'description' => 'Busca jurisprudencia y precedentes judiciales.',
                'category' => 'research',
                'schema_definition' => json_encode(array(
                    'name' => 'jurisprudence_search',
                    'description' => 'Busca jurisprudencia y precedentes judiciales',
                    'parameters' => array(
                        'type' => 'OBJECT',
                        'properties' => array(
                            'keywords' => array(
                                'type' => 'STRING',
                                'description' => 'Palabras clave para la búsqueda'
                            ),
                            'court_level' => array(
                                'type' => 'STRING',
                                'description' => 'Nivel de tribunal'
                            ),
                            'date_range' => array(
                                'type' => 'STRING',
                                'description' => 'Rango de fechas'
                            )
                        ),
                        'required' => array('keywords')
                    )
                )),
                'is_enabled' => 0,
                'is_system' => 0
            )
        );

        foreach ($default_tools as $tool) {
            $wpdb->insert($tools_table, $tool);
        }
    }

    /**
     * Force creation of specialized agents (for existing installations)
     */
    public static function force_create_specialized_agents() {
        self::create_specialized_agents();
        error_log('LexAI: Forced creation of specialized agents completed.');
    }
}
