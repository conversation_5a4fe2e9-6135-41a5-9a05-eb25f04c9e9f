<?php
/**
 * LexAI Export Handler Class
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Include TCPDF and PhpWord if they are not already loaded by Composer
// This is a fallback, Composer should handle this.
if (!class_exists('TCPDF')) {
    require_once LEXAI_PLUGIN_DIR . 'vendor/tecnickcom/tcpdf/tcpdf.php';
}
if (!class_exists('PhpOffice\PhpWord\PhpWord')) {
    require_once LEXAI_PLUGIN_DIR . 'vendor/phpoffice/phpword/bootstrap.php';
}

use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;


/**
 * LexAI Export Handler Class
 */
class LexAI_Export_Handler {

    /**
     * Main export method for conversations - handles all formats
     * (Existing method, will be kept for conversation export)
     */
    public function export_conversation($messages, $format = 'txt') {
        switch ($format) {
            case 'pdf':
                return $this->export_conversation_pdf_internal($messages);
            case 'docx':
                return $this->export_conversation_docx_internal($messages);
            case 'md':
                return $this->export_as_markdown($messages);
            case 'txt':
            default:
                return $this->export_as_text($messages);
        }
    }

    /**
     * New public method for single message export
     * Called by handle_export_single_message in LexAI_Ajax
     */
    public function export_content($content, $format, $filename_base) {
        $result = ['success' => false, 'message' => ''];
        $temp_file = tempnam(sys_get_temp_dir(), 'lexai_export_');
        $final_filename = $filename_base;
        $mime_type = '';

        try {
            switch ($format) {
                case 'pdf':
                    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
                    $pdf->SetCreator(PDF_CREATOR);
                    $pdf->SetAuthor('LexAI');
                    $pdf->SetTitle($filename_base);
                    $pdf->SetSubject('Exportación de Mensaje LexAI');
                    $pdf->SetMargins(15, 15, 15);
                    $pdf->SetAutoPageBreak(TRUE, 15);
                    $pdf->SetFont('helvetica', '', 10);
                    $pdf->AddPage();
                    $pdf->writeHTML($this->format_html_content($content), true, false, true, false, '');
                    $pdf->Output($temp_file, 'F'); // Save to file
                    $mime_type = 'application/pdf';
                    $final_filename .= '.pdf';
                    break;

                case 'docx':
                    $phpWord = new PhpWord();
                    $section = $phpWord->addSection();
                    $section->addText($this->get_message_plain_text($content), ['name' => 'Arial', 'size' => 11]);
                    $objWriter = IOFactory::createWriter($phpWord, 'Word2007');
                    $objWriter->save($temp_file);
                    $mime_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                    $final_filename .= '.docx';
                    break;

                case 'md':
                    $markdown_content = $this->format_markdown_content($content);
                    file_put_contents($temp_file, $markdown_content);
                    $mime_type = 'text/markdown';
                    $final_filename .= '.md';
                    break;

                case 'txt':
                default:
                    $plain_text_content = $this->get_message_plain_text($content);
                    file_put_contents($temp_file, $plain_text_content);
                    $mime_type = 'text/plain';
                    $final_filename .= '.txt';
                    break;
            }

            // Move the temporary file to a web-accessible location
            $upload_dir = wp_upload_dir();
            $export_dir = $upload_dir['basedir'] . '/lexai_exports';
            if (!file_exists($export_dir)) {
                wp_mkdir_p($export_dir);
            }

            $destination_path = $export_dir . '/' . sanitize_file_name($final_filename);
            if (rename($temp_file, $destination_path)) {
                $result['success'] = true;
                $result['download_url'] = $upload_dir['baseurl'] . '/lexai_exports/' . sanitize_file_name($final_filename);
                $result['filename'] = sanitize_file_name($final_filename);
            } else {
                $result['message'] = __('Failed to move exported file.', 'lexai');
            }

        } catch (Exception $e) {
            $result['message'] = $e->getMessage();
            error_log('LexAI Export Error: ' . $e->getMessage());
        } finally {
            if (file_exists($temp_file)) {
                unlink($temp_file); // Clean up temp file
            }
        }
        return $result;
    }

    /**
     * Export as plain text
     */
    private function export_as_text($messages) {
        $content = "DOCUMENTO DE CONSULTA LEGAL\n";
        $content .= str_repeat('=', 50) . "\n\n";
        $content .= "Fecha de generación: " . date('d/m/Y H:i') . "\n";
        $content .= "Generado por: LexAI - Asistente Legal Inteligente\n\n";
        $content .= str_repeat('=', 50) . "\n\n";
        
        $section_number = 1;
        foreach ($messages as $message) {
            $role = $message['role'] === 'user' ? 'CONSULTA' : 'RESPUESTA LEGAL';
            $timestamp = isset($message['created_at']) ? date('d/m/Y H:i', strtotime($message['created_at'])) : date('d/m/Y H:i');
            
            $content .= $section_number . ". " . $role . "\n";
            $content .= "Fecha y hora: " . $timestamp . "\n";
            $content .= str_repeat('-', 40) . "\n";
            $content .= $this->get_message_plain_text($message['content']) . "\n\n";
            $content .= str_repeat('_', 50) . "\n\n";
            $section_number++;
        }
        
        $content .= "\n" . str_repeat('=', 50) . "\n";
        $content .= "Fin del documento\n";
        $content .= "Este documento fue generado automáticamente por LexAI\n";
        $content .= "Para más información visite: https://lexai.com\n";
        
        return array(
            'content' => $content,
            'mime_type' => 'text/plain',
            'filename' => 'conversacion_legal_' . date('YmdHis') . '.txt'
        );
    }

    /**
     * Export as Markdown
     */
    private function export_as_markdown($messages) {
        $content = "# Documento de Consulta Legal\n\n";
        $content .= "**Fecha de generación:** " . date('d/m/Y H:i') . "\n";
        $content .= "**Generado por:** LexAI - Asistente Legal Inteligente\n\n";
        $content .= "---\n\n";
        
        $section_number = 1;
        foreach ($messages as $message) {
            $role = $message['role'] === 'user' ? 'CONSULTA' : 'RESPUESTA LEGAL';
            $timestamp = isset($message['created_at']) ? date('d/m/Y H:i', strtotime($message['created_at'])) : date('d/m/Y H:i');
            
            $content .= "## {$section_number}. {$role}\n\n";
            $content .= "**Fecha y hora:** {$timestamp}\n\n";
            // Use a more robust HTML to Markdown conversion if possible, or ensure input is clean
            $content .= $this->format_markdown_content($message['content']) . "\n\n";
            $content .= "---\n\n";
            $section_number++;
        }
        
        $content .= "\n*Este documento fue generado automáticamente por LexAI*\n";
        
        return array(
            'content' => $content,
            'mime_type' => 'text/markdown',
            'filename' => 'conversacion_legal_' . date('YmdHis') . '.md'
        );
    }

    /**
     * Export conversation as professional PDF
     */
    private function export_conversation_pdf_internal($messages) {
        if (!class_exists('TCPDF')) {
            throw new Exception('La librería TCPDF no está instalada. Ejecuta `composer require tecnickcom/tcpdf`.');
        }

        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

        $pdf->SetCreator(PDF_CREATOR);
        $pdf->SetAuthor('LexAI');
        $pdf->SetTitle('Conversación Legal');
        $pdf->SetSubject('Exportación de Conversación Legal');

        $pdf->SetHeaderData(PDF_HEADER_LOGO, PDF_HEADER_LOGO_WIDTH, 'Conversación Legal', 'Generado por LexAI');
        $pdf->setFooterData(array(0,64,0), array(0,64,128));

        $pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
        $pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));

        $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);

        $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
        $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
        $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);

        $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);

        $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

        if (@file_exists(dirname(__FILE__).'/lang/eng.php')) {
            require_once(dirname(__FILE__).'/lang/eng.php');
            $pdf->setLanguageArray($l);
        }

        $pdf->SetFont('helvetica', '', 10);
        $pdf->AddPage();

        $html_content = $this->generate_pdf_html($messages);
        $pdf->writeHTML($html_content, true, false, true, false, '');

        $filename = 'conversacion_legal_' . date('YmdHis') . '.pdf';
        $temp_file = tempnam(sys_get_temp_dir(), 'LexAI_PDF_');
        $pdf->Output($temp_file, 'F'); // Save to file

        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/lexai_exports';
        if (!file_exists($export_dir)) {
            wp_mkdir_p($export_dir);
        }
        $destination_path = $export_dir . '/' . sanitize_file_name($filename);
        if (rename($temp_file, $destination_path)) {
            return [
                'success' => true,
                'download_url' => $upload_dir['baseurl'] . '/lexai_exports/' . sanitize_file_name($filename),
                'filename' => sanitize_file_name($filename)
            ];
        } else {
            throw new Exception(__('Failed to move exported PDF file.', 'lexai'));
        }
    }

    /**
     * Export conversation as professional DOCX
     */
    private function export_conversation_docx_internal($messages) {
        if (!class_exists('PhpOffice\PhpWord\PhpWord')) {
            throw new Exception('La librería PhpWord no está instalada. Ejecuta `composer require phpoffice/phpword`.');
        }

        $phpWord = new PhpWord();
        $section = $phpWord->addSection();

        $section->addText('Conversación Legal', ['name' => 'Arial', 'size' => 16, 'bold' => true]);
        $section->addTextBreak(1);
        $section->addText('Generado por LexAI el ' . date('d/m/Y H:i'), ['name' => 'Arial', 'size' => 10, 'italic' => true]);
        $section->addTextBreak(2);

        foreach ($messages as $message) {
            $role = $message->role === 'user' ? 'CONSULTA' : 'RESPUESTA LEGAL';
            $timestamp = date('d/m/Y H:i', strtotime($message->created_at));

            $section->addText(strtoupper($role) . ' (' . $timestamp . ')', ['name' => 'Arial', 'size' => 12, 'bold' => true]);
            // Convert HTML content to plain text for DOCX
            $section->addText($this->get_message_plain_text($message->content), ['name' => 'Arial', 'size' => 11]);
            $section->addTextBreak(1);
        }

        $filename = 'conversacion_legal_' . date('YmdHis') . '.docx';
        $temp_file = tempnam(sys_get_temp_dir(), 'LexAI_DOCX_');
        $objWriter = IOFactory::createWriter($phpWord, 'Word2007');
        $objWriter->save($temp_file);

        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/lexai_exports';
        if (!file_exists($export_dir)) {
            wp_mkdir_p($export_dir);
        }
        $destination_path = $export_dir . '/' . sanitize_file_name($filename);
        if (rename($temp_file, $destination_path)) {
            return [
                'success' => true,
                'download_url' => $upload_dir['baseurl'] . '/lexai_exports/' . sanitize_file_name($filename),
                'filename' => sanitize_file_name($filename)
            ];
        } else {
            throw new Exception(__('Failed to move exported DOCX file.', 'lexai'));
        }
    }

    /**
     * Generate professional PDF HTML
     */
    private function generate_pdf_html($messages) {
        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {
                    font-family: "Times New Roman", serif;
                    font-size: 12pt;
                    line-height: 1.6;
                    margin: 2cm;
                    color: #000;
                }
                .header {
                    text-align: center;
                    border-bottom: 2px solid #000;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }
                .document-title {
                    font-size: 16pt;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }
                .section {
                    margin-bottom: 25px;
                    page-break-inside: avoid;
                }
                .section-header {
                    font-weight: bold;
                    font-size: 14pt;
                    border-bottom: 1px solid #333;
                    padding-bottom: 5px;
                    margin-bottom: 15px;
                    text-transform: uppercase;
                }
                .timestamp {
                    font-size: 10pt;
                    color: #666;
                    margin-bottom: 10px;
                }
                .content {
                    text-align: justify;
                    margin-bottom: 20px;
                    padding: 10px;
                    border-left: 3px solid #ddd;
                    background-color: #f9f9f9;
                }
                .footer {
                    position: fixed;
                    bottom: 2cm;
                    left: 2cm;
                    right: 2cm;
                    border-top: 1px solid #000;
                    padding-top: 10px;
                    font-size: 10pt;
                    text-align: center;
                }
                @page {
                    margin: 2cm;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="document-title">Documento de Consulta Legal</div>
                <div style="margin-top: 10px; font-size: 12pt;">
                    Fecha de generación: ' . date('d/m/Y H:i') . '
                </div>
            </div>
        ';
        
        $section_number = 1;
        foreach ($messages as $message) {
            $role = $message->role === 'user' ? 'CONSULTA' : 'RESPUESTA LEGAL';
            $timestamp = date('d/m/Y H:i', strtotime($message->created_at));
            
            $html .= '
            <div class="section">
                <div class="section-header">' . $section_number . '. ' . $role . '</div>
                <div class="timestamp">Fecha y hora: ' . $timestamp . '</div>
                <div class="content">' . nl2br($this->format_html_content($message->content)) . '</div>
            </div>
            ';
            $section_number++;
        }
        
        $html .= '
            <div class="footer">
                <div>Documento generado automáticamente - Página {PAGENO} de {nb}</div>
            </div>
        </body>
        </html>';
        
        return $html;
    }

    /**
     * Format content for HTML (for PDF generation)
     * This will convert Markdown to HTML.
     */
    private function format_html_content($content) {
        // Use a Markdown parser if available, otherwise simple nl2br and strip_tags
        // For now, let's assume the content might contain basic markdown and convert it to HTML
        // A more robust solution would involve a dedicated Markdown parsing library.
        $html = $content;

        // Basic Markdown to HTML conversion
        $html = preg_replace('/^### (.*$)/m', '<h3>$1</h3>', $html);
        $html = preg_replace('/^## (.*$)/m', '<h2>$1</h2>', $html);
        $html = preg_replace('/^# (.*$)/m', '<h1>$1</h1>', $html);
        $html = preg_replace('/\\*\\*(.*?)\\*\\*/s', '<strong>$1</strong>', $html);
        $html = preg_replace('/\\*(.*?)\\*/s', '<em>$1</em>', $html);
        $html = preg_replace('/`(.*?)`/s', '<code>$1</code>', $html);
        $html = preg_replace('/^\\s*[-*+] (.*$)/m', '<ul><li>$1</li></ul>', $html);
        $html = preg_replace('/^\\s*\\d+\\.\\s(.*$)/m', '<ol><li>$1</li></ol>', $html);
        $html = preg_replace('/^> (.*$)/m', '<blockquote>$1</blockquote>', $html);

        // Convert newlines to <br> tags, but only if not already within a block element
        $html = nl2br($html, false); // Use false to not add XHTML compliant <br />

        return $html;
    }

    /**
     * Format content for Markdown
     * This will ensure the content is properly formatted as Markdown.
     * If the input is HTML, it will attempt to convert it to Markdown.
     */
    private function format_markdown_content($content) {
        // If the content is already Markdown, just return it.
        // If it's HTML, we need to convert it to Markdown.
        // For a robust solution, a library like `league/html-to-markdown` would be ideal.
        // For now, we'll do a basic conversion.

        $markdown = $content;

        // Convert HTML tags to Markdown equivalents
        $markdown = str_replace(['<strong>', '</strong>'], '**', $markdown);
        $markdown = str_replace(['<em>', '</em>'], '*', $markdown);
        $markdown = str_replace(['<code>', '</code>'], '`', $markdown);
        $markdown = preg_replace('/<h1>(.*?)<\\/h1>/s', '# $1', $markdown);
        $markdown = preg_replace('/<h2>(.*?)<\\/h2>/s', '## $1', $markdown);
        $markdown = preg_replace('/<h3>(.*?)<\\/h3>/s', '### $1', $markdown);
        $markdown = preg_replace('/<ul><li>(.*?)<\\/li><\\/ul>/s', '- $1', $markdown);
        $markdown = preg_replace('/<ol><li>(.*?)<\\/li><\\/ol>/s', '1. $1', $markdown);
        $markdown = preg_replace('/<blockquote>(.*?)<\\/blockquote>/s', '> $1', $markdown);
        $markdown = str_replace(['<br>', '<br/>', '<br />'], "\n", $markdown);
        $markdown = str_replace(['<p>', '</p>'], "\n\n", $markdown);

        // Remove any remaining HTML tags
        $markdown = strip_tags($markdown);

        // Trim whitespace and ensure consistent newlines
        $markdown = trim($markdown);
        $markdown = preg_replace('/\\n{3,}/', "\n\n", $markdown); // Reduce multiple newlines

        return $markdown;
    }

    /**
     * Copy message content as plain text
     */
    public function get_message_plain_text($content) {
        // Remove all HTML tags and markdown formatting
        $text = strip_tags($content);
        
        // Remove markdown formatting
        $text = preg_replace('/\\*\\*(.*?)\\*\\*/', '$1', $text); // Bold
        $text = preg_replace('/\\*(.*?)\\*/', '$1', $text); // Italic
        $text = preg_replace('/`(.*?)`/', '$1', $text); // Code
        $text = preg_replace('/#{1,6}\\s/', '', $text); // Headers
        $text = preg_replace('/^\\s*[-*+]\\s/m', '', $text); // Lists
        $text = preg_replace('/^\\s*\\d+\\.\\s/m', '', $text); // Numbered lists
        
        // Clean up extra whitespace
        $text = preg_replace('/\\n\\s*\\n/', "\n\n", $text);
        $text = trim($text);
        
        return $text;
    }
}