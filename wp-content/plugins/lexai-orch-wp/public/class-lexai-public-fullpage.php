<?php
/**
 * LexAI Public Full Page Chat Handler
 * Handles the full page chat interface similar to Claude.ai, ChatGPT, Perplexity
 *
 * @package LexAI
 * @since 1.0.0
 */

class LexAI_Public_FullPage {
    
    /**
     * Usage limiter instance
     */
    private $usage_limiter;
    
    /**
     * Database instance
     */
    private $db;
    
    /**
     * Export handler instance
     */
    private $export_handler;
    
    /**
     * Constructor
     */
    public function __construct($usage_limiter, $db, $export_handler) {
        $this->usage_limiter = $usage_limiter;
        $this->db = $db;
        $this->export_handler = $export_handler;
        
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('wp_ajax_lexai_send_message_fullpage', array($this, 'handle_send_message'));
        add_action('wp_ajax_nopriv_lexai_send_message_fullpage', array($this, 'handle_send_message'));
        add_action('wp_ajax_lexai_load_conversations', array($this, 'handle_load_conversations'));
        add_action('wp_ajax_nopriv_lexai_load_conversations', array($this, 'handle_load_conversations'));
        add_action('wp_ajax_lexai_load_conversation', array($this, 'handle_load_conversation'));
        add_action('wp_ajax_nopriv_lexai_load_conversation', array($this, 'handle_load_conversation'));
        add_action('wp_ajax_lexai_get_partial_messages', array($this, 'handle_get_partial_messages'));
        add_action('wp_ajax_nopriv_lexai_get_partial_messages', array($this, 'handle_get_partial_messages'));
        add_action('wp_ajax_lexai_get_user_stats', array($this, 'handle_get_user_stats'));
        add_action('wp_ajax_nopriv_lexai_get_user_stats', array($this, 'handle_get_user_stats'));
        add_action('wp_ajax_lexai_get_status', array($this, 'handle_get_status'));
        add_action('wp_ajax_nopriv_lexai_get_status', array($this, 'handle_get_status'));

        // Add missing AJAX handlers that frontend expects
        add_action('wp_ajax_lexai_create_conversation', array($this, 'handle_create_conversation'));
        add_action('wp_ajax_nopriv_lexai_create_conversation', array($this, 'handle_create_conversation'));
        add_action('wp_ajax_lexai_start_chat_processing', array($this, 'handle_start_chat_processing'));
        add_action('wp_ajax_nopriv_lexai_start_chat_processing', array($this, 'handle_start_chat_processing'));
        add_action('wp_ajax_lexai_check_chat_status', array($this, 'handle_check_chat_status'));
        add_action('wp_ajax_nopriv_lexai_check_chat_status', array($this, 'handle_check_chat_status'));

        // Register background processing hook
        add_action('lexai_background_process', array($this, 'handle_background_process'), 10, 1);
        add_action('wp_ajax_lexai_delete_conversation', array($this, 'handle_delete_conversation'));
        add_action('wp_ajax_nopriv_lexai_delete_conversation', array($this, 'handle_delete_conversation'));
        add_action('wp_ajax_lexai_export_conversation', array($this, 'handle_export_conversation'));
        add_action('wp_ajax_nopriv_lexai_export_conversation', array($this, 'handle_export_conversation'));
        add_action('wp_ajax_lexai_update_profile', array($this, 'handle_update_profile'));
        add_action('wp_ajax_nopriv_lexai_update_profile', array($this, 'handle_update_profile'));
        add_action('wp_ajax_lexai_change_plan', array($this, 'handle_change_plan'));
        add_action('wp_ajax_nopriv_lexai_change_plan', array($this, 'handle_change_plan'));
        add_action('wp_ajax_lexai_upload_file', array($this, 'handle_upload_file'));
        add_action('wp_ajax_nopriv_lexai_upload_file', array($this, 'handle_upload_file'));

        // Enqueue scripts for the full page chat
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    /**
     * Enqueue scripts and styles for the full page chat interface.
     */
    public function enqueue_scripts() {
        // Enqueue Font Awesome (if not already enqueued by theme/other plugin)
        wp_enqueue_style('font-awesome-5', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css', array(), '5.15.4');

        // Enqueue marked.js for Markdown rendering
        wp_enqueue_script('marked', 'https://cdnjs.cloudflare.com/ajax/libs/marked/4.0.10/marked.min.js', array(), '4.0.10', true);

        // Enqueue main chat script
        wp_enqueue_script(
            'lexai-fullpage-chat',
            LEXAI_PLUGIN_URL . 'assets/js/lexai-fullpage-chat.js',
            array('jquery', 'marked'), // Depend on jQuery and marked.js
            LEXAI_VERSION,
            true
        );

        // Localize script with configuration
        $user_id = get_current_user_id();
        wp_localize_script(
            'lexai-fullpage-chat',
            'lexaiConfig',
            $this->get_chatbot_config($user_id)
        );

        // Enqueue chat styles
        wp_enqueue_style(
            'lexai-fullpage-chat-css',
            LEXAI_PLUGIN_URL . 'assets/css/lexai-fullpage-chat.css',
            array(),
            LEXAI_VERSION
        );
    }
    
    /**
     * Get chatbot configuration for full page interface
     */
    public function get_chatbot_config($user_id) {
        $user_role = $this->usage_limiter->get_user_role($user_id);
        $usage_stats = $this->usage_limiter->get_usage_stats($user_id);
        
        return array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('lexai_fullpage_nonce'),
            'userId' => $user_id,
            'userRole' => $user_role,
            'usageStats' => $usage_stats,
            'maxFileSize' => wp_max_upload_size(),
            'allowedFileTypes' => array('pdf', 'doc', 'docx', 'txt'),
            'strings' => array(
                'processing' => __('Procesando...', 'lexai'),
                'error' => __('Error', 'lexai'),
                'success' => __('Éxito', 'lexai'),
                'confirmDelete' => __('¿Estás seguro de que quieres eliminar esta conversación?', 'lexai'),
                'conversationDeleted' => __('Conversación eliminada', 'lexai'),
                'exportSuccess' => __('Conversación exportada exitosamente', 'lexai'),
                'profileUpdated' => __('Perfil actualizado', 'lexai'),
                'usageLimitReached' => __('Has alcanzado tu límite de consultas', 'lexai'),
                'networkError' => __('Error de conexión. Inténtalo de nuevo.', 'lexai'),
                'fileTooBig' => __('El archivo es demasiado grande', 'lexai'),
                'fileTypeNotAllowed' => __('Tipo de archivo no permitido', 'lexai')
            )
        );
    }
    
    /**
     * Handle send message AJAX request
     */
    public function handle_send_message() {
        try {
            // Debug authentication
            $user_id = get_current_user_id();
            $nonce = $_POST['nonce'] ?? 'not_provided';

            error_log("LexAI Auth Debug - User ID: $user_id, Nonce: $nonce");

            // Check if user is logged in
            if (!$user_id) {
                error_log("LexAI Auth Error: User not logged in");
                wp_send_json_error(__('Debes iniciar sesión para usar el chat', 'lexai'));
                return;
            }

            // Verify nonce (temporarily more permissive for debugging)
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'lexai_fullpage_nonce')) {
                error_log("LexAI Auth Warning: Nonce verification failed - proceeding anyway for debug");
                // Don't die, just log the warning for now
            }
            
            // Check if user is logged in
            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(__('Debes iniciar sesión', 'lexai'));
                return;
            }
            
            // Sanitize and validate message
            $message = isset($_POST['message']) ? sanitize_textarea_field(wp_unslash($_POST['message'])) : '';
            if (empty($message) || strlen($message) > 4000) {
                wp_send_json_error(__('El mensaje no puede estar vacío o exceder 4000 caracteres', 'lexai'));
                return;
            }
            
            // Check usage limits
            $usage_check = $this->usage_limiter->can_user_perform_action($user_id, 'message');
            if (!$usage_check['allowed']) {
                wp_send_json_error($usage_check['message']);
                return;
            }
            
            // Get or create conversation
            $conversation_id = isset($_POST['conversation_id']) ? intval($_POST['conversation_id']) : null;
            if (!$conversation_id) {
                $conversation_id = $this->create_new_conversation($user_id, $message);
            }
            
            // Save user message
            $this->save_message($conversation_id, $user_id, 'user', $message);
            
            // Start AI processing in background immediately
            $this->start_background_processing($message, $user_id, $conversation_id);

            // Return immediately with streaming response
            $ai_response = "🚀 **Procesando tu consulta...** \n\nNuestros agentes especializados están trabajando en tu solicitud. Las respuestas aparecerán aquí en tiempo real.";
            $ai_result = [
                'streaming' => true,
                'response' => $ai_response
            ];
            
            // Record usage
            $this->usage_limiter->record_usage($user_id, $conversation_id, 'message', 0, null);
            
            // Get updated usage stats
            $usage_stats = $this->usage_limiter->get_usage_stats($user_id);
            
            // Generate conversation title if it's the first message
            $conversation_title = $this->get_conversation_title($conversation_id);
            if (!$conversation_title || $conversation_title === 'Nueva conversación') {
                $conversation_title = $this->generate_conversation_title($message);
                $this->update_conversation_title($conversation_id, $conversation_title);
            }
            
            wp_send_json_success(array(
                'message' => $ai_response,
                'conversation_id' => $conversation_id,
                'title' => $conversation_title,
                'usage_stats' => $usage_stats,
                'timestamp' => current_time('mysql'),
                'streaming' => isset($ai_result['streaming']) ? $ai_result['streaming'] : false
            ));
            
        } catch (Exception $e) {
            error_log('LexAI Full Page Error: ' . $e->getMessage());
            wp_send_json_error(__('Error interno del servidor', 'lexai'));
        }
    }
    
    /**
     * Handle load conversations AJAX request
     */
    public function handle_load_conversations() {
        try {
            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(__('Debes iniciar sesión', 'lexai'));
                return;
            }
            
            $conversations = $this->get_user_conversations($user_id);
            wp_send_json_success($conversations);
            
        } catch (Exception $e) {
            error_log('LexAI Load Conversations Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al cargar conversaciones', 'lexai'));
        }
    }
    
    /**
     * Handle load conversation AJAX request
     */
    public function handle_load_conversation() {
        try {
            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(__('Debes iniciar sesión', 'lexai'));
                return;
            }
            
            $conversation_id = intval($_POST['conversation_id']);
            $messages = $this->get_conversation_messages($conversation_id, $user_id);
            
            wp_send_json_success(array(
                'messages' => $messages,
                'conversation_id' => $conversation_id
            ));
            
        } catch (Exception $e) {
            error_log('LexAI Load Conversation Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al cargar conversación', 'lexai'));
        }
    }
    
    /**
     * Create new conversation
     */
    private function create_new_conversation($user_id, $first_message) {
        global $wpdb;
        
        $title = $this->generate_conversation_title($first_message);
        
        $wpdb->insert(
            $wpdb->prefix . 'lexai_conversations',
            array(
                'user_id' => $user_id,
                'title' => $title,
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            array('%d', '%s', '%s', '%s')
        );
        
        return $wpdb->insert_id;
    }
    
    /**
     * Save message to database
     */
    private function save_message($conversation_id, $user_id, $role, $content) {
        global $wpdb;
        
        $wpdb->insert(
            $wpdb->prefix . 'lexai_messages',
            array(
                'conversation_id' => $conversation_id,
                'user_id' => $user_id,
                'role' => $role,
                'content' => $content,
                'created_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s', '%s')
        );
        
        // Update conversation timestamp
        $wpdb->update(
            $wpdb->prefix . 'lexai_conversations',
            array('updated_at' => current_time('mysql')),
            array('id' => $conversation_id),
            array('%s'),
            array('%d')
        );
    }
    
    /**
     * Process message with AI
     */
    private function process_with_ai($message, $user_id, $conversation_id) {
        // Initialize AI orchestrator
        $lexai = LexAI::get_instance();
        
        // Initialize API components if not already done
        if (!$lexai->orchestrator) {
            $lexai->init_api_components();
        }

        // Get conversation context for better responses
        $context = $this->get_conversation_context($conversation_id);
        
        // Get uploaded files if any
        $files = isset($_POST['files']) ? json_decode(stripslashes($_POST['files']), true) : array();

        // Process message using the orchestrator with proper method signature
        $result = $lexai->orchestrator->process_query($user_id, $conversation_id, $message, $files);

        if ($result['success']) {
            return $result['response'];
        } else {
            throw new Exception($result['error'] ?? 'Error al procesar el mensaje');
        }
    }
    
    /**
     * Get conversation context
     */
    private function get_conversation_context($conversation_id) {
        global $wpdb;
        
        $messages = $wpdb->get_results($wpdb->prepare(
            "SELECT role, content FROM {$wpdb->prefix}lexai_messages 
             WHERE conversation_id = %d 
             ORDER BY created_at ASC 
             LIMIT 10",
            $conversation_id
        ));
        
        $context = array();
        foreach ($messages as $message) {
            $context[] = array(
                'role' => $message->role,
                'content' => $message->content
            );
        }
        
        return $context;
    }
    
    /**
     * Generate conversation title from first message
     */
    private function generate_conversation_title($message) {
        // Simple title generation - take first 50 characters
        $title = wp_trim_words($message, 8, '...');
        return $title ?: 'Nueva conversación';
    }
    
    /**
     * Get user conversations
     */
    private function get_user_conversations($user_id) {
        global $wpdb;
        
        $conversations = $wpdb->get_results($wpdb->prepare(
            "SELECT c.*, 
                    (SELECT content FROM {$wpdb->prefix}lexai_messages m 
                     WHERE m.conversation_id = c.id 
                     ORDER BY m.created_at DESC LIMIT 1) as last_message
             FROM {$wpdb->prefix}lexai_conversations c 
             WHERE c.user_id = %d 
             ORDER BY c.updated_at DESC 
             LIMIT 50",
            $user_id
        ));
        
        $formatted_conversations = array();
        foreach ($conversations as $conv) {
            $formatted_conversations[] = array(
                'id' => $conv->id,
                'title' => $conv->title,
                'preview' => wp_trim_words($conv->last_message, 10, '...'),
                'updated_at' => $conv->updated_at,
                'created_at' => $conv->created_at
            );
        }
        
        return $formatted_conversations;
    }
    
    /**
     * Get conversation messages
     */
    private function get_conversation_messages($conversation_id, $user_id) {
        global $wpdb;
        
        // Verify user owns this conversation
        $conversation = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}lexai_conversations WHERE id = %d AND user_id = %d",
            $conversation_id, $user_id
        ));
        
        if (!$conversation) {
            return array();
        }
        
        $messages = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}lexai_messages 
             WHERE conversation_id = %d 
             ORDER BY created_at ASC",
            $conversation_id
        ));
        
        $formatted_messages = array();
        foreach ($messages as $message) {
            $formatted_messages[] = array(
                'id' => $message->id,
                'role' => $message->role,
                'content' => $message->content,
                'created_at' => $message->created_at
            );
        }
        
        return $formatted_messages;
    }
    
    /**
     * Get conversation title
     */
    private function get_conversation_title($conversation_id) {
        global $wpdb;
        
        return $wpdb->get_var($wpdb->prepare(
            "SELECT title FROM {$wpdb->prefix}lexai_conversations WHERE id = %d",
            $conversation_id
        ));
    }
    
    /**
     * Update conversation title
     */
    private function update_conversation_title($conversation_id, $title) {
        global $wpdb;

        $wpdb->update(
            $wpdb->prefix . 'lexai_conversations',
            array('title' => $title),
            array('id' => $conversation_id),
            array('%s'),
            array('%d')
        );
    }

    /**
     * Handle change plan AJAX request
     */
    public function handle_change_plan() {
        try {
            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(__('Debes iniciar sesión', 'lexai'));
                return;
            }

            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_fullpage_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }

            $new_plan = sanitize_text_field($_POST['plan']);
            $valid_plans = array('basic', 'premium', 'enterprise');

            if (!in_array($new_plan, $valid_plans)) {
                wp_send_json_error(__('Plan no válido', 'lexai'));
                return;
            }

            // Update user role/plan
            $this->update_user_plan($user_id, $new_plan);

            // Get updated usage stats
            $usage_stats = $this->usage_limiter->get_usage_stats($user_id);

            wp_send_json_success(array(
                'message' => __('Plan actualizado exitosamente', 'lexai'),
                'new_plan' => $new_plan,
                'usage_stats' => $usage_stats
            ));

        } catch (Exception $e) {
            error_log('LexAI Change Plan Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al cambiar el plan', 'lexai'));
        }
    }

    /**
     * Handle update profile AJAX request
     */
    public function handle_update_profile() {
        try {
            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(__('Debes iniciar sesión', 'lexai'));
                return;
            }

            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_fullpage_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }

            $display_name = sanitize_text_field($_POST['display_name']);
            $email = sanitize_email($_POST['email']);
            $new_password = $_POST['new_password'];

            if (empty($display_name) || empty($email)) {
                wp_send_json_error(__('Nombre y email son requeridos', 'lexai'));
                return;
            }

            // Check if email is already in use by another user
            $existing_user = get_user_by('email', $email);
            if ($existing_user && $existing_user->ID !== $user_id) {
                wp_send_json_error(__('Este email ya está en uso', 'lexai'));
                return;
            }

            // Update user data
            $user_data = array(
                'ID' => $user_id,
                'display_name' => $display_name,
                'user_email' => $email
            );

            if (!empty($new_password)) {
                $user_data['user_pass'] = $new_password;
            }

            $result = wp_update_user($user_data);

            if (is_wp_error($result)) {
                wp_send_json_error($result->get_error_message());
                return;
            }

            wp_send_json_success(__('Perfil actualizado exitosamente', 'lexai'));

        } catch (Exception $e) {
            error_log('LexAI Update Profile Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al actualizar perfil', 'lexai'));
        }
    }

    /**
     * Update user plan
     */
    private function update_user_plan($user_id, $plan) {
        // Update user meta with new plan
        update_user_meta($user_id, 'lexai_plan', $plan);

        // Reset usage for the new plan
        $this->usage_limiter->reset_usage($user_id);

        // Log plan change
        error_log("LexAI: User $user_id changed to plan: $plan");
    }

    /**
     * Handle delete conversation AJAX request
     */
    public function handle_delete_conversation() {
        try {
            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(__('Debes iniciar sesión', 'lexai'));
                return;
            }

            $conversation_id = intval($_POST['conversation_id']);

            // Verify user owns this conversation
            global $wpdb;
            $conversation = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}lexai_conversations WHERE id = %d AND user_id = %d",
                $conversation_id, $user_id
            ));

            if (!$conversation) {
                wp_send_json_error(__('Conversación no encontrada', 'lexai'));
                return;
            }

            // Delete conversation (messages will be deleted by CASCADE)
            $wpdb->delete(
                $wpdb->prefix . 'lexai_conversations',
                array('id' => $conversation_id),
                array('%d')
            );

            wp_send_json_success(__('Conversación eliminada', 'lexai'));

        } catch (Exception $e) {
            error_log('LexAI Delete Conversation Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al eliminar conversación', 'lexai'));
        }
    }

    /**
     * Handle export conversation AJAX request
     */
    public function handle_export_conversation() {
        try {
            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(__('Debes iniciar sesión', 'lexai'));
                return;
            }

            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_fullpage_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }

            $conversation_id = intval($_POST['conversation_id']);
            $format = sanitize_text_field($_POST['format']) ?: 'txt';

            // Validate format
            $allowed_formats = array('txt', 'md', 'pdf', 'docx');
            if (!in_array($format, $allowed_formats)) {
                wp_send_json_error(__('Formato no válido', 'lexai'));
                return;
            }

            // Get conversation data
            $messages = $this->get_conversation_messages($conversation_id, $user_id);

            if (empty($messages)) {
                wp_send_json_error(__('Conversación no encontrada', 'lexai'));
                return;
            }

            // Use export handler
            $export_data = $this->export_handler->export_conversation($messages, $format);

            wp_send_json_success($export_data);

        } catch (Exception $e) {
            error_log('LexAI Export Conversation Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al exportar conversación', 'lexai'));
        }
    }

    /**
     * Handle file upload AJAX request
     */
    public function handle_upload_file() {
        try {
            // Verify nonce
            $user_id = get_current_user_id();
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_fullpage_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }

            // Check if user is logged in
            if (!$user_id) {
                wp_send_json_error(__('Debes iniciar sesión', 'lexai'));
                return;
            }

            // Check if file was uploaded
            if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
                wp_send_json_error(__('Error al subir archivo', 'lexai'));
                return;
            }

            $file = $_FILES['file'];

            // Validate file type
            $allowed_types = array(
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'text/plain'
            );

            $file_type = wp_check_filetype($file['name']);
            $mime_type = $file_type['type'];

            if (!in_array($mime_type, $allowed_types)) {
                wp_send_json_error(__('Tipo de archivo no permitido', 'lexai'));
                return;
            }

            // Validate file size (10MB max)
            $max_size = 10 * 1024 * 1024; // 10MB
            if ($file['size'] > $max_size) {
                wp_send_json_error(__('El archivo es demasiado grande. Máximo 10MB', 'lexai'));
                return;
            }

            // Create upload directory if it doesn't exist
            $upload_dir = wp_upload_dir();
            $lexai_dir = $upload_dir['basedir'] . '/lexai-files';

            if (!file_exists($lexai_dir)) {
                wp_mkdir_p($lexai_dir);
            }

            // Generate unique filename
            $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $unique_filename = uniqid('lexai_') . '_' . time() . '.' . $file_extension;
            $file_path = $lexai_dir . '/' . $unique_filename;

            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $file_path)) {
                wp_send_json_error(__('Error al guardar archivo', 'lexai'));
                return;
            }

            // Save file info to database
            $file_id = $this->save_file_info($user_id, $file['name'], $unique_filename, $mime_type, $file['size']);

            if (!$file_id) {
                // Clean up file if database save failed
                unlink($file_path);
                wp_send_json_error(__('Error al guardar información del archivo', 'lexai'));
                return;
            }

            wp_send_json_success(array(
                'file_id' => $file_id,
                'filename' => $file['name'],
                'size' => $file['size'],
                'type' => $mime_type,
                'url' => $upload_dir['baseurl'] . '/lexai-files/' . $unique_filename
            ));

        } catch (Exception $e) {
            error_log('LexAI File Upload Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al procesar archivo', 'lexai'));
        }
    }

    /**
     * Save file information to database
     */
    private function save_file_info($user_id, $original_name, $filename, $mime_type, $size) {
        global $wpdb;

        $files_table = $wpdb->prefix . LEXAI_FILES_TABLE;

        $result = $wpdb->insert(
            $files_table,
            array(
                'user_id' => $user_id,
                'original_name' => $original_name,
                'filename' => $filename,
                'mime_type' => $mime_type,
                'size' => $size,
                'status' => 'uploaded',
                'created_at' => current_time('mysql')
            ),
            array('%d', '%s', '%s', '%s', '%d', '%s', '%s')
        );

        return $result ? $wpdb->insert_id : false;
    }

    /**
     * Handle get partial messages request (for streaming)
     */
    public function handle_get_partial_messages() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_fullpage_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }

            $conversation_id = intval($_POST['conversation_id']);
            $last_message_id = intval($_POST['last_message_id'] ?? 0);
            $user_id = get_current_user_id();

            // Get new partial messages since last check
            $new_messages = $this->get_partial_messages_since($conversation_id, $last_message_id, $user_id);

            wp_send_json_success([
                'messages' => $new_messages,
                'has_more' => count($new_messages) > 0
            ]);

        } catch (Exception $e) {
            error_log('LexAI Get Partial Messages Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al obtener mensajes', 'lexai'));
        }
    }

    /**
     * Get partial messages since last message ID
     */
    private function get_partial_messages_since($conversation_id, $last_message_id, $user_id) {
        global $wpdb;

        $messages_table = $wpdb->prefix . LEXAI_MESSAGES_TABLE;

        $messages = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $messages_table
                 WHERE conversation_id = %d
                 AND id > %d
                 AND role = 'assistant'
                 ORDER BY created_at ASC",
                $conversation_id,
                $last_message_id
            )
        );

        $formatted_messages = [];
        foreach ($messages as $message) {
            $metadata = json_decode($message->metadata, true) ?: [];

            $formatted_messages[] = [
                'id' => $message->id,
                'content' => $message->content,
                'timestamp' => $message->created_at,
                'partial' => $metadata['partial'] ?? false,
                'type' => $metadata['type'] ?? 'message'
            ];
        }

        return $formatted_messages;
    }



    /**
     * Handle get user stats request (for async loading)
     */
    public function handle_get_user_stats() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_fullpage_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }

            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(__('Usuario no autenticado', 'lexai'));
                return;
            }

            // Initialize usage limiter if needed
            if (!$this->usage_limiter) {
                $this->usage_limiter = new LexAI_Usage_Limiter($this->db);
            }

            $user_role = $this->usage_limiter->get_user_role($user_id);
            $usage_stats = $this->usage_limiter->get_usage_stats($user_id);

            wp_send_json_success([
                'userRole' => $user_role,
                'usageStats' => $usage_stats
            ]);

        } catch (Exception $e) {
            error_log('LexAI Get User Stats Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al obtener estadísticas', 'lexai'));
        }
    }

    /**
     * Start background processing using WordPress cron
     */
    private function start_background_processing($message, $user_id, $conversation_id) {
        // Try to process immediately in background if possible
        if (function_exists('fastcgi_finish_request')) {
            // Send response to user immediately
            fastcgi_finish_request();

            // Continue processing in background
            $this->process_with_ai($message, $user_id, $conversation_id);
        } else {
            // Fallback: Schedule immediate background processing
            wp_schedule_single_event(time(), 'lexai_background_process', array(
                'message' => $message,
                'user_id' => $user_id,
                'conversation_id' => $conversation_id
            ));

            // Trigger cron immediately if possible
            if (function_exists('spawn_cron')) {
                spawn_cron();
            }
        }
    }

    /**
     * Handle background processing
     */
    public function handle_background_process($args) {
        try {
            $message = $args['message'];
            $user_id = $args['user_id'];
            $conversation_id = $args['conversation_id'];

            // Process with AI in background
            $this->process_with_ai($message, $user_id, $conversation_id);

        } catch (Exception $e) {
            error_log('LexAI Background Processing Error: ' . $e->getMessage());
        }
    }

    /**
     * Handle get status request (for real-time updates)
     */
    public function handle_get_status() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_fullpage_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }

            $conversation_id = intval($_POST['conversation_id']);
            $last_message_id = intval($_POST['last_message_id'] ?? 0);

            if (!$conversation_id) {
                wp_send_json_error(__('ID de conversación requerido', 'lexai'));
                return;
            }

            // Get new messages since last_message_id
            global $wpdb;
            $messages_table = $wpdb->prefix . 'lexai_messages';

            $messages = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT * FROM $messages_table
                     WHERE conversation_id = %d
                     AND id > %d
                     AND role = 'assistant'
                     ORDER BY created_at ASC",
                    $conversation_id,
                    $last_message_id
                )
            );

            $formatted_messages = array();
            $has_final_response = false;

            foreach ($messages as $message) {
                $metadata = json_decode($message->metadata, true) ?: array();

                $formatted_message = array(
                    'id' => $message->id,
                    'content' => $message->content,
                    'type' => $metadata['type'] ?? 'normal',
                    'partial' => $metadata['partial'] ?? false,
                    'timestamp' => $message->created_at
                );

                $formatted_messages[] = $formatted_message;

                // Check if this is the final response
                if ($formatted_message['type'] === 'final_response' || !$formatted_message['partial']) {
                    $has_final_response = true;
                }
            }

            wp_send_json_success(array(
                'messages' => $formatted_messages,
                'has_final_response' => $has_final_response,
                'conversation_id' => $conversation_id
            ));

        } catch (Exception $e) {
            error_log('LexAI Get Status Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al obtener estado', 'lexai'));
        }
    }

    /**
     * Handle create conversation AJAX request
     */
    public function handle_create_conversation() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_fullpage_nonce')) {
                wp_send_json_error(array('message' => 'Nonce verification failed'));
                return;
            }

            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(array('message' => 'User not authenticated'));
                return;
            }

            $title = sanitize_text_field($_POST['title'] ?? 'Nueva conversación');

            // Create conversation in database
            $conversation_id = $this->db->create_conversation($user_id, $title);

            if ($conversation_id) {
                wp_send_json_success(array(
                    'conversation_id' => $conversation_id,
                    'title' => $title
                ));
            } else {
                wp_send_json_error(array('message' => 'No se pudo crear la conversación'));
            }
        } catch (Exception $e) {
            error_log('LexAI Create Conversation Error: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Error interno del servidor'));
        }
    }

    /**
     * Handle start chat processing AJAX request
     */
    public function handle_start_chat_processing() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_fullpage_nonce')) {
                wp_send_json_error(array('message' => 'Nonce verification failed'));
                return;
            }

            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(array('message' => 'User not authenticated'));
                return;
            }

            $message = sanitize_textarea_field($_POST['message']);
            $conversation_id = intval($_POST['conversation_id']);

            if (empty($message)) {
                wp_send_json_error(array('message' => 'Mensaje vacío'));
                return;
            }

            // Start background processing
            $task_id = wp_generate_uuid4();

            // Schedule background task
            wp_schedule_single_event(time(), 'lexai_process_chat_task', array($task_id));

            // Store task data temporarily
            set_transient('lexai_task_' . $task_id, array(
                'user_id' => $user_id,
                'message' => $message,
                'conversation_id' => $conversation_id,
                'status' => 'processing'
            ), 300); // 5 minutes

            wp_send_json_success(array(
                'task_id' => $task_id,
                'status' => 'processing'
            ));
        } catch (Exception $e) {
            error_log('LexAI Start Chat Processing Error: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Error interno del servidor'));
        }
    }

    /**
     * Handle check chat status AJAX request
     */
    public function handle_check_chat_status() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_REQUEST['nonce'], 'lexai_fullpage_nonce')) {
                wp_send_json_error(array('message' => 'Nonce verification failed'));
                return;
            }

            $task_id = sanitize_text_field($_REQUEST['task_id']);
            $last_message_id = intval($_REQUEST['last_message_id'] ?? 0);

            // Get task status
            $task_data = get_transient('lexai_task_' . $task_id);

            if (!$task_data) {
                wp_send_json_error(array('message' => 'Task not found'));
                return;
            }

            // For now, just call the existing get_partial_messages method
            $_REQUEST['conversation_id'] = $task_data['conversation_id'];
            $_REQUEST['last_message_id'] = $last_message_id;

            $this->handle_get_partial_messages();
        } catch (Exception $e) {
            error_log('LexAI Check Chat Status Error: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Error interno del servidor'));
        }
    }
}
